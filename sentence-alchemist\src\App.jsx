import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Home, Target, BarChart3, Calendar } from 'lucide-react';
import TrainingPage from './pages/TrainingPage';
import ReviewPage from './pages/ReviewPage';
import StatsPage from './pages/StatsPage';
import CalendarPage from './pages/CalendarPage';
import SentenceDetailPage from './pages/SentenceDetailPage';
import OnboardingPage from './pages/OnboardingPage';

const AppContainer = styled.div`
  min-height: 100vh;
  background: #f7fafc;
`;

const Navigation = styled.nav`
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const NavContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const Logo = styled.h1`
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
`;

const NavMenu = styled.div`
  display: flex;
  gap: 1rem;
`;

const NavButton = styled.button`
  background: ${props => props.active ? 'linear-gradient(135deg, #667eea, #764ba2)' : 'transparent'};
  color: ${props => props.active ? 'white' : '#4a5568'};
  border: 2px solid ${props => props.active ? 'transparent' : '#e2e8f0'};
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    background: ${props => props.active ? 'linear-gradient(135deg, #667eea, #764ba2)' : '#f7fafc'};
    border-color: ${props => props.active ? 'transparent' : '#cbd5e0'};
  }
`;

function App() {
  const [currentPage, setCurrentPage] = useState('training');
  const [selectedSentenceId, setSelectedSentenceId] = useState(null);
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // 检查用户是否已经完成引导
    const onboardingCompleted = localStorage.getItem('onboardingCompleted');
    if (!onboardingCompleted) {
      setShowOnboarding(true);
    }
  }, []);

  const handleSentenceDetail = (sentenceId) => {
    setSelectedSentenceId(sentenceId);
    setCurrentPage('sentence-detail');
  };

  const handleBackFromDetail = () => {
    setCurrentPage('training');
    setSelectedSentenceId(null);
  };

  const handleOnboardingComplete = () => {
    setShowOnboarding(false);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'sentence-detail':
        return (
          <SentenceDetailPage
            sentenceId={selectedSentenceId}
            onBack={handleBackFromDetail}
          />
        );
      case 'review':
        return <ReviewPage onSentenceDetail={handleSentenceDetail} />;
      case 'stats':
        return <StatsPage onSentenceDetail={handleSentenceDetail} />;
      case 'calendar':
        return <CalendarPage />;
      case 'training':
      default:
        return <TrainingPage onSentenceDetail={handleSentenceDetail} />;
    }
  };

  // 如果需要显示引导页面，直接返回引导页面
  if (showOnboarding) {
    return <OnboardingPage onComplete={handleOnboardingComplete} />;
  }

  return (
    <AppContainer>
      <Navigation>
        <NavContainer>
          <Logo>句之炼金 Sentence Alchemist</Logo>
          <NavMenu>
            <NavButton
              active={currentPage === 'training'}
              onClick={() => setCurrentPage('training')}
            >
              <Home size={18} />
              练习模式
            </NavButton>
            <NavButton
              active={currentPage === 'review'}
              onClick={() => setCurrentPage('review')}
            >
              <Target size={18} />
              智能复习
            </NavButton>
            <NavButton
              active={currentPage === 'stats'}
              onClick={() => setCurrentPage('stats')}
            >
              <BarChart3 size={18} />
              学习统计
            </NavButton>
            <NavButton
              active={currentPage === 'calendar'}
              onClick={() => setCurrentPage('calendar')}
            >
              <Calendar size={18} />
              学习日历
            </NavButton>
          </NavMenu>
        </NavContainer>
      </Navigation>

      {renderCurrentPage()}
    </AppContainer>
  );
}

export default App;
