import React, { useState } from 'react';
import styled from 'styled-components';
import { ChevronRight, ChevronLeft, Target, BarChart3, Calendar, BookOpen, Volume2, Trophy } from 'lucide-react';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
`;

const OnboardingCard = styled.div`
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  padding: 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
`;

const StepIndicator = styled.div`
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
`;

const StepDot = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.active ? '#667eea' : '#e2e8f0'};
  transition: all 0.3s ease;
`;

const Title = styled.h1`
  color: #2d3748;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
`;

const Subtitle = styled.p`
  color: #718096;
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
`;

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin: 2rem 0;
`;

const FeatureCard = styled.div`
  background: #f7fafc;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
`;

const FeatureIcon = styled.div`
  background: #667eea;
  color: white;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
`;

const FeatureTitle = styled.h3`
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const FeatureDesc = styled.p`
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
`;

const Button = styled.button`
  background: ${props => props.primary ? 'linear-gradient(135deg, #667eea, #764ba2)' : 'transparent'};
  color: ${props => props.primary ? 'white' : '#4a5568'};
  border: 2px solid ${props => props.primary ? 'transparent' : '#e2e8f0'};
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
`;

const SkipButton = styled.button`
  background: transparent;
  border: none;
  color: #718096;
  cursor: pointer;
  text-decoration: underline;
  font-size: 0.9rem;
  
  &:hover {
    color: #4a5568;
  }
`;

const PreferenceContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin: 2rem 0;
`;

const PreferenceItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f7fafc;
  border-radius: 8px;
`;

const PreferenceLabel = styled.span`
  color: #2d3748;
  font-weight: 500;
`;

const Select = styled.select`
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.5rem;
  color: #4a5568;
`;

const OnboardingPage = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState({
    difficulty: 'beginner',
    preferredScene: 'all',
    dailyGoal: '10'
  });

  const steps = [
    {
      title: '欢迎来到句之炼金！',
      subtitle: '一个智能的英语句子学习平台，帮助您快速掌握实用英语表达',
      content: (
        <div>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎯</div>
          <p style={{ color: '#718096', fontSize: '1.1rem' }}>
            通过智能匹配练习，让英语学习变得更加高效有趣
          </p>
        </div>
      )
    },
    {
      title: '强大的功能特性',
      subtitle: '为您提供全方位的学习体验',
      content: (
        <FeatureGrid>
          <FeatureCard>
            <FeatureIcon><Target size={24} /></FeatureIcon>
            <FeatureTitle>智能练习</FeatureTitle>
            <FeatureDesc>根据您的学习进度智能推荐句子</FeatureDesc>
          </FeatureCard>
          <FeatureCard>
            <FeatureIcon><BarChart3 size={24} /></FeatureIcon>
            <FeatureTitle>学习统计</FeatureTitle>
            <FeatureDesc>详细的学习数据和进度分析</FeatureDesc>
          </FeatureCard>
          <FeatureCard>
            <FeatureIcon><Calendar size={24} /></FeatureIcon>
            <FeatureTitle>复习提醒</FeatureTitle>
            <FeatureDesc>基于遗忘曲线的智能复习系统</FeatureDesc>
          </FeatureCard>
          <FeatureCard>
            <FeatureIcon><Trophy size={24} /></FeatureIcon>
            <FeatureTitle>成就系统</FeatureTitle>
            <FeatureDesc>丰富的成就徽章激励学习</FeatureDesc>
          </FeatureCard>
        </FeatureGrid>
      )
    },
    {
      title: '个性化设置',
      subtitle: '根据您的需求定制学习体验',
      content: (
        <PreferenceContainer>
          <PreferenceItem>
            <PreferenceLabel>学习难度</PreferenceLabel>
            <Select 
              value={preferences.difficulty}
              onChange={(e) => setPreferences({...preferences, difficulty: e.target.value})}
            >
              <option value="beginner">初级</option>
              <option value="intermediate">中级</option>
              <option value="advanced">高级</option>
            </Select>
          </PreferenceItem>
          <PreferenceItem>
            <PreferenceLabel>偏好场景</PreferenceLabel>
            <Select 
              value={preferences.preferredScene}
              onChange={(e) => setPreferences({...preferences, preferredScene: e.target.value})}
            >
              <option value="all">全部场景</option>
              <option value="airport">机场</option>
              <option value="restaurant">餐厅</option>
              <option value="hotel">酒店</option>
              <option value="shopping">购物</option>
              <option value="work">工作</option>
            </Select>
          </PreferenceItem>
          <PreferenceItem>
            <PreferenceLabel>每日目标</PreferenceLabel>
            <Select 
              value={preferences.dailyGoal}
              onChange={(e) => setPreferences({...preferences, dailyGoal: e.target.value})}
            >
              <option value="5">5个句子</option>
              <option value="10">10个句子</option>
              <option value="20">20个句子</option>
              <option value="30">30个句子</option>
            </Select>
          </PreferenceItem>
        </PreferenceContainer>
      )
    },
    {
      title: '开始您的学习之旅！',
      subtitle: '一切准备就绪，让我们开始学习吧',
      content: (
        <div>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🚀</div>
          <p style={{ color: '#718096', fontSize: '1.1rem', marginBottom: '1rem' }}>
            您的个性化学习环境已经配置完成
          </p>
          <div style={{ background: '#f7fafc', padding: '1rem', borderRadius: '8px', textAlign: 'left' }}>
            <p><strong>学习难度：</strong>{preferences.difficulty === 'beginner' ? '初级' : preferences.difficulty === 'intermediate' ? '中级' : '高级'}</p>
            <p><strong>偏好场景：</strong>{preferences.preferredScene === 'all' ? '全部场景' : preferences.preferredScene}</p>
            <p><strong>每日目标：</strong>{preferences.dailyGoal}个句子</p>
          </div>
        </div>
      )
    }
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // 保存用户偏好并完成引导
      localStorage.setItem('userPreferences', JSON.stringify(preferences));
      localStorage.setItem('onboardingCompleted', 'true');
      onComplete();
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    localStorage.setItem('onboardingCompleted', 'true');
    onComplete();
  };

  const currentStepData = steps[currentStep];

  return (
    <PageContainer>
      <OnboardingCard>
        <StepIndicator>
          {steps.map((_, index) => (
            <StepDot key={index} active={index <= currentStep} />
          ))}
        </StepIndicator>

        <Title>{currentStepData.title}</Title>
        <Subtitle>{currentStepData.subtitle}</Subtitle>
        
        {currentStepData.content}

        <ButtonContainer>
          <div>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                <ChevronLeft size={18} />
                上一步
              </Button>
            )}
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <SkipButton onClick={handleSkip}>跳过引导</SkipButton>
            <Button primary onClick={handleNext}>
              {currentStep === steps.length - 1 ? '开始学习' : '下一步'}
              {currentStep < steps.length - 1 && <ChevronRight size={18} />}
            </Button>
          </div>
        </ButtonContainer>
      </OnboardingCard>
    </PageContainer>
  );
};

export default OnboardingPage;
