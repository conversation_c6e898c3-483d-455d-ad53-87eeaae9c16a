import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Target, 
  Brain, 
  Calendar,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { learningStateManager } from '../utils/learningStateManager';
import { reviewQueueManager } from '../utils/reviewQueueManager';

const PanelContainer = styled.div`
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
`;

const PanelTitle = styled.h2`
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const StatCard = styled.div`
  background: ${props => props.bgColor || '#f7fafc'};
  border: 1px solid ${props => props.borderColor || '#e2e8f0'};
  border-radius: 12px;
  padding: 1rem;
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const StatIcon = styled.div`
  color: ${props => props.color || '#667eea'};
`;

const StatValue = styled.div`
  font-size: 1.8rem;
  color: #2d3748;
  font-weight: 700;
  margin-bottom: 0.25rem;
`;

const StatSubtext = styled.div`
  font-size: 0.8rem;
  color: #718096;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

const MemoryStrengthSection = styled.div`
  margin-bottom: 1.5rem;
`;

const MemoryStrengthGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
`;

const MemoryCard = styled.div`
  background: ${props => props.bgColor};
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  border: 2px solid ${props => props.borderColor};
`;

const MemoryLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const MemoryCount = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${props => props.color};
  margin-bottom: 0.25rem;
`;

const MemoryPercentage = styled.div`
  font-size: 0.8rem;
  color: #718096;
`;

const EfficiencySection = styled.div`
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
`;

const EfficiencyTitle = styled.h3`
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const EfficiencyMetrics = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
`;

const MetricItem = styled.div`
  text-align: center;
`;

const MetricValue = styled.div`
  font-size: 1.3rem;
  font-weight: 700;
  color: ${props => props.color || '#2d3748'};
  margin-bottom: 0.25rem;
`;

const MetricLabel = styled.div`
  font-size: 0.8rem;
  color: #718096;
  font-weight: 500;
`;

const TrendIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: ${props => props.positive ? '#38a169' : '#e53e3e'};
  font-size: 0.8rem;
  font-weight: 500;
`;

const ReviewStatsPanel = ({ sentences }) => {
  const [stats, setStats] = useState(null);
  const [memoryAnalysis, setMemoryAnalysis] = useState(null);
  const [efficiencyAnalysis, setEfficiencyAnalysis] = useState(null);
  const [reviewStats, setReviewStats] = useState(null);

  useEffect(() => {
    if (sentences && sentences.length > 0) {
      updateStats();
    }
  }, [sentences]);

  const updateStats = () => {
    // 获取学习统计
    const learningStats = learningStateManager.getLearningStats(sentences);
    
    // 获取记忆强度分析
    const memoryStrengthAnalysis = learningStateManager.getMemoryStrengthAnalysis(sentences);
    
    // 获取学习效率分析
    const efficiencyStats = learningStateManager.getLearningEfficiencyAnalysis(sentences);
    
    // 获取复习统计
    const reviewQueueStats = reviewQueueManager.getReviewStats();
    
    setStats(learningStats);
    setMemoryAnalysis(memoryStrengthAnalysis);
    setEfficiencyAnalysis(efficiencyStats);
    setReviewStats(reviewQueueStats);
  };

  if (!stats || !memoryAnalysis || !efficiencyAnalysis) {
    return <div>加载统计数据中...</div>;
  }

  const formatTime = (milliseconds) => {
    if (milliseconds < 1000) return `${Math.round(milliseconds)}ms`;
    return `${(milliseconds / 1000).toFixed(1)}s`;
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  return (
    <PanelContainer>
      <PanelTitle>
        <BarChart3 size={20} />
        学习数据分析
      </PanelTitle>

      <StatsGrid>
        <StatCard bgColor="#f0fff4" borderColor="#9ae6b4">
          <StatHeader>
            <StatLabel>已掌握句子</StatLabel>
            <StatIcon color="#38a169">
              <Target size={16} />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.mastered}</StatValue>
          <StatSubtext>
            总计 {stats.total} 个句子
          </StatSubtext>
        </StatCard>

        <StatCard bgColor="#fffaf0" borderColor="#fbd38d">
          <StatHeader>
            <StatLabel>需要复习</StatLabel>
            <StatIcon color="#d69e2e">
              <Clock size={16} />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.needReview}</StatValue>
          <StatSubtext>
            {reviewStats && `预计 ${Math.ceil(reviewStats.estimatedTotalTime / 60)} 分钟`}
          </StatSubtext>
        </StatCard>

        <StatCard bgColor="#f7fafc" borderColor="#cbd5e0">
          <StatHeader>
            <StatLabel>平均掌握度</StatLabel>
            <StatIcon color="#667eea">
              <Brain size={16} />
            </StatIcon>
          </StatHeader>
          <StatValue>{stats.averageMasteryLevel.toFixed(1)}</StatValue>
          <StatSubtext>
            满分 5.0 分
          </StatSubtext>
        </StatCard>
      </StatsGrid>

      <MemoryStrengthSection>
        <PanelTitle>
          <Brain size={20} />
          记忆强度分布
        </PanelTitle>
        
        <MemoryStrengthGrid>
          <MemoryCard bgColor="#f0fff4" borderColor="#9ae6b4">
            <MemoryLabel>强记忆</MemoryLabel>
            <MemoryCount color="#38a169">{memoryAnalysis.strong.length}</MemoryCount>
            <MemoryPercentage>
              {formatPercentage(memoryAnalysis.strong.length / memoryAnalysis.totalSentences)}
            </MemoryPercentage>
          </MemoryCard>
          
          <MemoryCard bgColor="#fffaf0" borderColor="#fbd38d">
            <MemoryLabel>中等记忆</MemoryLabel>
            <MemoryCount color="#d69e2e">{memoryAnalysis.medium.length}</MemoryCount>
            <MemoryPercentage>
              {formatPercentage(memoryAnalysis.medium.length / memoryAnalysis.totalSentences)}
            </MemoryPercentage>
          </MemoryCard>
          
          <MemoryCard bgColor="#fed7d7" borderColor="#fc8181">
            <MemoryLabel>弱记忆</MemoryLabel>
            <MemoryCount color="#e53e3e">{memoryAnalysis.weak.length}</MemoryCount>
            <MemoryPercentage>
              {formatPercentage(memoryAnalysis.weak.length / memoryAnalysis.totalSentences)}
            </MemoryPercentage>
          </MemoryCard>
        </MemoryStrengthGrid>
      </MemoryStrengthSection>

      <EfficiencySection>
        <EfficiencyTitle>
          <Activity size={20} />
          学习效率分析
        </EfficiencyTitle>
        
        <EfficiencyMetrics>
          <MetricItem>
            <MetricValue color="#667eea">
              {formatPercentage(efficiencyAnalysis.averageAccuracy)}
            </MetricValue>
            <MetricLabel>平均准确率</MetricLabel>
          </MetricItem>
          
          <MetricItem>
            <MetricValue color="#38a169">
              {formatTime(efficiencyAnalysis.averageResponseTime)}
            </MetricValue>
            <MetricLabel>平均响应时间</MetricLabel>
          </MetricItem>
          
          <MetricItem>
            <MetricValue color="#d69e2e">
              {(efficiencyAnalysis.efficiencyScore * 100).toFixed(1)}
            </MetricValue>
            <MetricLabel>效率分数</MetricLabel>
          </MetricItem>
          
          <MetricItem>
            <MetricValue>
              {efficiencyAnalysis.totalAttempts}
            </MetricValue>
            <MetricLabel>总答题次数</MetricLabel>
            {efficiencyAnalysis.improvementTrend !== 0 && (
              <TrendIndicator positive={efficiencyAnalysis.improvementTrend > 0}>
                {efficiencyAnalysis.improvementTrend > 0 ? (
                  <TrendingUp size={12} />
                ) : (
                  <TrendingDown size={12} />
                )}
                {efficiencyAnalysis.improvementTrend > 0 ? '进步中' : '需加强'}
              </TrendIndicator>
            )}
          </MetricItem>
        </EfficiencyMetrics>
      </EfficiencySection>
    </PanelContainer>
  );
};

export default ReviewStatsPanel;
