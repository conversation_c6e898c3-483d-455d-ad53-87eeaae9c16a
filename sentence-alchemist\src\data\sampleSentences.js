/**
 * 句子数据模型定义
 * @typedef {Object} Sentence
 * @property {number} id - 句子唯一标识符
 * @property {string} english - 英文句子
 * @property {string} chinese - 中文翻译（正确答案）
 * @property {string[]} distractors - 干扰项数组（3个错误选项）
 * @property {string} scene - 场景分类（airport, restaurant, hotel, shopping, work, travel等）
 * @property {string} difficulty - 难度等级（beginner, intermediate, advanced）
 * @property {string[]} tags - 标签数组（语法点、词汇类型等）
 * @property {string} pronunciation - 发音标记（可选）
 * @property {string} chinesePronunciation - 中文发音提示（可选）
 * @property {string} explanation - 句子解释或用法说明（可选）
 * @property {number} frequency - 使用频率（1-5，5为最常用）
 * @property {Object} learningData - 学习数据
 * @property {number} learningData.correctCount - 答对次数
 * @property {number} learningData.totalAttempts - 总尝试次数
 * @property {Date} learningData.lastReviewed - 最后复习时间
 * @property {Date} learningData.nextReview - 下次复习时间
 * @property {number} learningData.masteryLevel - 掌握程度（0-5）
 */

// 场景分类常量
export const SCENES = {
  AIRPORT: 'airport',
  RESTAURANT: 'restaurant',
  HOTEL: 'hotel',
  SHOPPING: 'shopping',
  WORK: 'work',
  TRAVEL: 'travel',
  DAILY: 'daily',
  MEDICAL: 'medical',
  EDUCATION: 'education'
};

// 难度等级常量
export const DIFFICULTY_LEVELS = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced'
};

// 示例句子数据
export const sampleSentences = [
  {
    id: 1,
    english: "Can I have a window seat, please?",
    chinese: "可以给我一个靠窗的座位吗？",
    distractors: [
      "我能看见一个窗户座位吗？",
      "我有一个靠窗的座位。",
      "请坐在窗户旁边。"
    ],
    scene: SCENES.AIRPORT,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['polite-request', 'travel', 'seating'],
    pronunciation: "/kæn aɪ hæv ə ˈwɪndoʊ sit, pliz/",
    chinesePronunciation: "坎 爱 哈夫 额 温多 西特，普利兹",
    explanation: "在机场或飞机上礼貌地请求靠窗座位的常用表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 2,
    english: "How much does this cost?",
    chinese: "这个多少钱？",
    distractors: [
      "这个花费了多少？",
      "这个成本是多少？",
      "多少钱能买这个？"
    ],
    scene: SCENES.SHOPPING,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['price-inquiry', 'shopping', 'question'],
    pronunciation: "/haʊ mʌtʃ dʌz ðɪs kɔst/",
    chinesePronunciation: "豪 马奇 达兹 迪斯 考斯特",
    explanation: "询问商品价格的基本表达方式",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 3,
    english: "Could you recommend a good restaurant nearby?",
    chinese: "你能推荐一家附近的好餐厅吗？",
    distractors: [
      "你能建议一个好的餐厅在附近吗？",
      "你可以推荐附近的餐厅吗？",
      "能告诉我附近哪里有好餐厅吗？"
    ],
    scene: SCENES.TRAVEL,
    difficulty: DIFFICULTY_LEVELS.INTERMEDIATE,
    tags: ['recommendation', 'restaurant', 'polite-request'],
    pronunciation: "/kʊd ju ˌrekəˈmend ə gʊd ˈrestərɑnt ˈnɪrˌbaɪ/",
    chinesePronunciation: "库德 尤 瑞克门德 额 古德 瑞斯特朗特 尼尔拜",
    explanation: "礼貌地请求推荐餐厅的表达方式，使用了could表示更礼貌的语气",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 4,
    english: "I'd like to check in, please.",
    chinese: "我想办理入住手续。",
    distractors: [
      "我想要检查一下。",
      "我想要登记入住。",
      "我要查看房间。"
    ],
    scene: SCENES.HOTEL,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['hotel', 'check-in', 'polite-request'],
    pronunciation: "/aɪd laɪk tu tʃek ɪn, pliz/",
    chinesePronunciation: "爱德 莱克 图 切克 因，普利兹",
    explanation: "酒店办理入住手续时的标准表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 5,
    english: "What time does the meeting start?",
    chinese: "会议什么时候开始？",
    distractors: [
      "会议开始的时间是什么？",
      "什么时候会议开始？",
      "会议几点钟开始？"
    ],
    scene: SCENES.WORK,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['time-inquiry', 'work', 'meeting'],
    pronunciation: "/wʌt taɪm dʌz ðə ˈmitɪŋ stɑrt/",
    chinesePronunciation: "沃特 泰姆 达兹 德 米听 斯塔特",
    explanation: "询问会议开始时间的常用表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  // 机场场景句子
  {
    id: 6,
    english: "Where is the departure gate?",
    chinese: "登机口在哪里？",
    distractors: [
      "出发门在哪里？",
      "离开的门在哪里？",
      "登机门在什么地方？"
    ],
    scene: SCENES.AIRPORT,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['direction', 'airport', 'gate'],
    pronunciation: "/wɛr ɪz ðə dɪˈpɑrtʃər geɪt/",
    chinesePronunciation: "维尔 椅日 德 蒂帕楚 盖特",
    explanation: "在机场询问登机口位置的常用表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 7,
    english: "I need to check my luggage.",
    chinese: "我需要托运行李。",
    distractors: [
      "我需要检查我的行李。",
      "我要查看我的行李。",
      "我需要确认我的行李。"
    ],
    scene: SCENES.AIRPORT,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['luggage', 'airport', 'check-in'],
    pronunciation: "/aɪ nid tu tʃek maɪ ˈlʌgɪdʒ/",
    explanation: "在机场办理行李托运时的表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  // 餐厅场景句子
  {
    id: 8,
    english: "I'd like to make a reservation for two.",
    chinese: "我想预订两人的座位。",
    distractors: [
      "我想要保留两个人。",
      "我要做两个人的预约。",
      "我想预定两个座位。"
    ],
    scene: SCENES.RESTAURANT,
    difficulty: DIFFICULTY_LEVELS.INTERMEDIATE,
    tags: ['reservation', 'restaurant', 'booking'],
    pronunciation: "/aɪd laɪk tu meɪk ə ˌrezərˈveɪʃən fɔr tu/",
    explanation: "在餐厅预订座位的礼貌表达方式",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 9,
    english: "What do you recommend?",
    chinese: "你推荐什么？",
    distractors: [
      "你建议什么？",
      "你介绍什么？",
      "你提议什么？"
    ],
    scene: SCENES.RESTAURANT,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['recommendation', 'restaurant', 'menu'],
    pronunciation: "/wʌt du ju ˌrekəˈmend/",
    explanation: "在餐厅询问服务员推荐菜品的简单表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 10,
    english: "Could I have the bill, please?",
    chinese: "请给我账单好吗？",
    distractors: [
      "我能有这个账单吗？",
      "请给我这个票据？",
      "我可以要账单吗？"
    ],
    scene: SCENES.RESTAURANT,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['bill', 'restaurant', 'payment'],
    pronunciation: "/kʊd aɪ hæv ðə bɪl, pliz/",
    explanation: "在餐厅礼貌地要求结账的表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  // 酒店场景句子
  {
    id: 11,
    english: "Do you have any rooms available?",
    chinese: "你们有空房间吗？",
    distractors: [
      "你们有任何房间可用吗？",
      "你们有一些房间空着吗？",
      "你们有房间能用吗？"
    ],
    scene: SCENES.HOTEL,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['availability', 'hotel', 'booking'],
    pronunciation: "/du ju hæv ˈeni rumz əˈveɪləbəl/",
    explanation: "询问酒店是否有空房的常用表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 12,
    english: "What time is checkout?",
    chinese: "退房时间是几点？",
    distractors: [
      "检查时间是几点？",
      "查看时间是什么时候？",
      "结账时间是几点？"
    ],
    scene: SCENES.HOTEL,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['checkout', 'hotel', 'time'],
    pronunciation: "/wʌt taɪm ɪz ˈtʃekˌaʊt/",
    explanation: "询问酒店退房时间的表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  // 购物场景句子
  {
    id: 13,
    english: "Do you have this in a different size?",
    chinese: "你们有不同尺码的吗？",
    distractors: [
      "你们有这个在不同大小吗？",
      "你们有这个其他尺寸的吗？",
      "你们有这个别的大小吗？"
    ],
    scene: SCENES.SHOPPING,
    difficulty: DIFFICULTY_LEVELS.INTERMEDIATE,
    tags: ['size', 'shopping', 'clothing'],
    pronunciation: "/du ju hæv ðɪs ɪn ə ˈdɪfərənt saɪz/",
    explanation: "在购物时询问是否有其他尺码的表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  {
    id: 14,
    english: "Can I try this on?",
    chinese: "我可以试穿这个吗？",
    distractors: [
      "我能试试这个吗？",
      "我可以尝试穿这个吗？",
      "我能试着穿这个吗？"
    ],
    scene: SCENES.SHOPPING,
    difficulty: DIFFICULTY_LEVELS.BEGINNER,
    tags: ['try-on', 'shopping', 'clothing'],
    pronunciation: "/kæn aɪ traɪ ðɪs ɑn/",
    explanation: "在服装店试穿衣服时的常用表达",
    frequency: 5,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  },
  // 工作场景句子
  {
    id: 15,
    english: "Could you send me the report by email?",
    chinese: "你能通过邮件发给我报告吗？",
    distractors: [
      "你能用邮件寄给我报告吗？",
      "你可以邮件发送报告给我吗？",
      "你能邮件传送报告给我吗？"
    ],
    scene: SCENES.WORK,
    difficulty: DIFFICULTY_LEVELS.INTERMEDIATE,
    tags: ['email', 'work', 'request'],
    pronunciation: "/kʊd ju send mi ðə rɪˈpɔrt baɪ ˈimeɪl/",
    explanation: "在工作中礼貌地请求通过邮件发送文件的表达",
    frequency: 4,
    learningData: {
      correctCount: 0,
      totalAttempts: 0,
      lastReviewed: null,
      nextReview: null,
      masteryLevel: 0
    }
  }
];

/**
 * 智能随机选择算法
 * 考虑学习历史、难度适应性和场景平衡
 */

/**
 * 获取随机句子（基础版本，保持向后兼容）
 * @param {number[]} excludeIds - 要排除的句子ID数组
 * @returns {Object} 随机选择的句子
 */
export const getRandomSentence = (excludeIds = []) => {
  const availableSentences = sampleSentences.filter(
    sentence => !excludeIds.includes(sentence.id)
  );

  if (availableSentences.length === 0) {
    return sampleSentences[Math.floor(Math.random() * sampleSentences.length)];
  }

  return availableSentences[Math.floor(Math.random() * availableSentences.length)];
};

/**
 * 智能随机选择句子
 * @param {Object} options - 选择选项
 * @param {number[]} options.excludeIds - 要排除的句子ID数组
 * @param {string} options.preferredScene - 偏好场景
 * @param {string} options.targetDifficulty - 目标难度
 * @param {Object} options.userProfile - 用户学习档案
 * @param {boolean} options.adaptiveDifficulty - 是否启用自适应难度
 * @returns {Object} 智能选择的句子
 */
export const getSmartRandomSentence = (options = {}) => {
  const {
    excludeIds = [],
    preferredScene,
    targetDifficulty,
    userProfile = {},
    adaptiveDifficulty = false
  } = options;

  // 获取可用句子
  let candidates = sampleSentences.filter(
    sentence => !excludeIds.includes(sentence.id)
  );

  if (candidates.length === 0) {
    return sampleSentences[Math.floor(Math.random() * sampleSentences.length)];
  }

  // 场景筛选
  if (preferredScene) {
    const sceneMatches = candidates.filter(s => s.scene === preferredScene);
    if (sceneMatches.length > 0) {
      candidates = sceneMatches;
    }
  }

  // 难度筛选
  if (targetDifficulty) {
    const difficultyMatches = candidates.filter(s => s.difficulty === targetDifficulty);
    if (difficultyMatches.length > 0) {
      candidates = difficultyMatches;
    }
  }

  // 自适应难度调整
  if (adaptiveDifficulty && userProfile.averageAccuracy !== undefined) {
    candidates = adjustDifficultyBasedOnPerformance(candidates, userProfile);
  }

  // 加权随机选择
  return weightedRandomSelection(candidates, userProfile);
};

/**
 * 根据用户表现调整难度
 * @param {Object[]} candidates - 候选句子
 * @param {Object} userProfile - 用户档案
 * @returns {Object[]} 调整后的候选句子
 */
function adjustDifficultyBasedOnPerformance(candidates, userProfile) {
  const { averageAccuracy = 0.5, recentAccuracy = 0.5 } = userProfile;

  // 如果用户表现很好，增加难度
  if (averageAccuracy > 0.8 && recentAccuracy > 0.8) {
    const harderSentences = candidates.filter(s =>
      s.difficulty === DIFFICULTY_LEVELS.INTERMEDIATE ||
      s.difficulty === DIFFICULTY_LEVELS.ADVANCED
    );
    if (harderSentences.length > 0) {
      return harderSentences;
    }
  }

  // 如果用户表现不好，降低难度
  if (averageAccuracy < 0.5 && recentAccuracy < 0.5) {
    const easierSentences = candidates.filter(s =>
      s.difficulty === DIFFICULTY_LEVELS.BEGINNER
    );
    if (easierSentences.length > 0) {
      return easierSentences;
    }
  }

  return candidates;
}

/**
 * 加权随机选择
 * 根据句子的学习状态和重要性进行加权选择
 * @param {Object[]} candidates - 候选句子
 * @param {Object} userProfile - 用户档案
 * @returns {Object} 选择的句子
 */
function weightedRandomSelection(candidates, userProfile = {}) {
  // 计算每个句子的权重
  const weights = candidates.map(sentence => {
    let weight = 1;

    // 基于掌握程度的权重（掌握程度低的权重高）
    const masteryLevel = sentence.learningData?.masteryLevel || 0;
    weight *= Math.max(0.1, 1 - masteryLevel / 5);

    // 基于使用频率的权重
    weight *= (sentence.frequency || 1) / 5;

    // 基于错误率的权重（错误率高的权重高）
    const totalAttempts = sentence.learningData?.totalAttempts || 0;
    const correctCount = sentence.learningData?.correctCount || 0;
    if (totalAttempts > 0) {
      const errorRate = 1 - (correctCount / totalAttempts);
      weight *= 1 + errorRate;
    }

    // 基于复习时间的权重（需要复习的权重高）
    const nextReview = sentence.learningData?.nextReview;
    if (nextReview && new Date(nextReview) <= new Date()) {
      weight *= 2; // 需要复习的句子权重翻倍
    }

    return Math.max(0.01, weight); // 确保权重不为0
  });

  // 加权随机选择
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  let random = Math.random() * totalWeight;

  for (let i = 0; i < candidates.length; i++) {
    random -= weights[i];
    if (random <= 0) {
      return candidates[i];
    }
  }

  // 备选方案：返回最后一个
  return candidates[candidates.length - 1];
}

// 生成选项数组的函数
export const generateOptions = (sentence) => {
  const options = [sentence.chinese, ...sentence.distractors];
  
  // 随机打乱选项顺序
  for (let i = options.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [options[i], options[j]] = [options[j], options[i]];
  }
  
  // 返回选项数组和正确答案的索引
  const correctIndex = options.indexOf(sentence.chinese);
  
  return {
    options,
    correctIndex
  };
};
