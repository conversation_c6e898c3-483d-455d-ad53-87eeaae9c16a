/**
 * 学习记录追踪系统
 * 记录用户的学习历史、复习时间和学习效果分析
 */

// 学习活动类型
export const ACTIVITY_TYPE = {
  PRACTICE: 'practice',           // 练习
  REVIEW: 'review',              // 复习
  CHALLENGE: 'challenge',        // 挑战
  ADAPTIVE: 'adaptive',          // 自适应学习
  REINFORCEMENT: 'reinforcement' // 强化练习
};

// 学习结果类型
export const RESULT_TYPE = {
  CORRECT: 'correct',
  INCORRECT: 'incorrect',
  SKIPPED: 'skipped',
  TIMEOUT: 'timeout'
};

/**
 * 学习记录追踪器类
 */
export class LearningTracker {
  constructor() {
    this.sessionHistory = new Map();     // 学习会话历史
    this.dailyRecords = new Map();       // 每日学习记录
    this.weeklyStats = new Map();        // 每周统计
    this.monthlyStats = new Map();       // 每月统计
    this.learningStreaks = new Map();    // 学习连续记录
    this.currentSession = null;          // 当前学习会话
    
    // 加载历史数据
    this.loadFromStorage();
  }

  /**
   * 开始新的学习会话
   * @param {string} activityType - 活动类型
   * @param {Object} config - 会话配置
   * @returns {string} 会话ID
   */
  startSession(activityType, config = {}) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      activityType,
      startTime: new Date(),
      endTime: null,
      config,
      records: [],
      stats: {
        totalQuestions: 0,
        correctAnswers: 0,
        totalTime: 0,
        averageResponseTime: 0,
        accuracy: 0,
        streak: 0,
        maxStreak: 0
      },
      performance: {
        byDifficulty: {
          beginner: { correct: 0, total: 0 },
          intermediate: { correct: 0, total: 0 },
          advanced: { correct: 0, total: 0 }
        },
        byScene: {},
        responseTimeDistribution: [],
        accuracyTrend: []
      }
    };
    
    this.currentSession = session;
    this.sessionHistory.set(sessionId, session);
    
    return sessionId;
  }

  /**
   * 记录学习活动
   * @param {Object} record - 学习记录
   */
  recordActivity(record) {
    if (!this.currentSession) {
      console.warn('No active session to record activity');
      return;
    }

    const activityRecord = {
      timestamp: new Date(),
      sentenceId: record.sentenceId,
      sentence: record.sentence,
      selectedAnswer: record.selectedAnswer,
      correctAnswer: record.correctAnswer,
      isCorrect: record.isCorrect,
      responseTime: record.responseTime,
      resultType: record.resultType || (record.isCorrect ? RESULT_TYPE.CORRECT : RESULT_TYPE.INCORRECT),
      difficulty: record.difficulty,
      scene: record.scene,
      masteryLevelBefore: record.masteryLevelBefore,
      masteryLevelAfter: record.masteryLevelAfter,
      memoryStrengthBefore: record.memoryStrengthBefore,
      memoryStrengthAfter: record.memoryStrengthAfter
    };

    // 添加到当前会话
    this.currentSession.records.push(activityRecord);
    
    // 更新会话统计
    this.updateSessionStats(activityRecord);
    
    // 更新每日记录
    this.updateDailyRecord(activityRecord);
    
    // 保存到本地存储
    this.saveToStorage();
  }

  /**
   * 结束当前学习会话
   * @returns {Object} 会话总结
   */
  endSession() {
    if (!this.currentSession) {
      console.warn('No active session to end');
      return null;
    }

    this.currentSession.endTime = new Date();
    this.currentSession.stats.totalTime = 
      this.currentSession.endTime - this.currentSession.startTime;

    // 计算最终统计
    this.finalizeSessionStats();
    
    // 更新学习连续记录
    this.updateLearningStreak();
    
    // 更新周/月统计
    this.updatePeriodStats();
    
    const sessionSummary = this.generateSessionSummary();
    
    // 清除当前会话
    this.currentSession = null;
    
    // 保存到本地存储
    this.saveToStorage();
    
    return sessionSummary;
  }

  /**
   * 更新会话统计
   * @param {Object} record - 活动记录
   */
  updateSessionStats(record) {
    const stats = this.currentSession.stats;
    const performance = this.currentSession.performance;
    
    // 基础统计
    stats.totalQuestions++;
    if (record.isCorrect) {
      stats.correctAnswers++;
      stats.streak++;
      stats.maxStreak = Math.max(stats.maxStreak, stats.streak);
    } else {
      stats.streak = 0;
    }
    
    stats.accuracy = stats.correctAnswers / stats.totalQuestions;
    
    // 响应时间统计
    if (record.responseTime > 0) {
      const totalResponseTime = stats.averageResponseTime * (stats.totalQuestions - 1) + record.responseTime;
      stats.averageResponseTime = totalResponseTime / stats.totalQuestions;
      
      performance.responseTimeDistribution.push(record.responseTime);
    }
    
    // 按难度统计
    if (record.difficulty && performance.byDifficulty[record.difficulty]) {
      performance.byDifficulty[record.difficulty].total++;
      if (record.isCorrect) {
        performance.byDifficulty[record.difficulty].correct++;
      }
    }
    
    // 按场景统计
    if (record.scene) {
      if (!performance.byScene[record.scene]) {
        performance.byScene[record.scene] = { correct: 0, total: 0 };
      }
      performance.byScene[record.scene].total++;
      if (record.isCorrect) {
        performance.byScene[record.scene].correct++;
      }
    }
    
    // 准确率趋势
    performance.accuracyTrend.push({
      questionNumber: stats.totalQuestions,
      accuracy: stats.accuracy,
      isCorrect: record.isCorrect
    });
  }

  /**
   * 更新每日记录
   * @param {Object} record - 活动记录
   */
  updateDailyRecord(record) {
    const dateKey = this.getDateKey(new Date());
    
    if (!this.dailyRecords.has(dateKey)) {
      this.dailyRecords.set(dateKey, {
        date: dateKey,
        totalQuestions: 0,
        correctAnswers: 0,
        totalTime: 0,
        sessionsCount: 0,
        activities: [],
        achievements: []
      });
    }
    
    const dailyRecord = this.dailyRecords.get(dateKey);
    dailyRecord.totalQuestions++;
    if (record.isCorrect) {
      dailyRecord.correctAnswers++;
    }
    
    dailyRecord.activities.push({
      timestamp: record.timestamp,
      activityType: this.currentSession.activityType,
      isCorrect: record.isCorrect,
      responseTime: record.responseTime
    });
  }

  /**
   * 更新学习连续记录
   */
  updateLearningStreak() {
    const today = this.getDateKey(new Date());
    const yesterday = this.getDateKey(new Date(Date.now() - 24 * 60 * 60 * 1000));
    
    if (!this.learningStreaks.has('current')) {
      this.learningStreaks.set('current', {
        count: 0,
        startDate: today,
        lastDate: today
      });
    }
    
    const currentStreak = this.learningStreaks.get('current');
    
    if (currentStreak.lastDate === yesterday) {
      // 连续学习
      currentStreak.count++;
      currentStreak.lastDate = today;
    } else if (currentStreak.lastDate === today) {
      // 今天已经学习过了，不增加计数
      return;
    } else {
      // 中断了，重新开始
      currentStreak.count = 1;
      currentStreak.startDate = today;
      currentStreak.lastDate = today;
    }
    
    // 更新最长连续记录
    const maxStreak = this.learningStreaks.get('max') || { count: 0 };
    if (currentStreak.count > maxStreak.count) {
      this.learningStreaks.set('max', {
        count: currentStreak.count,
        startDate: currentStreak.startDate,
        endDate: today
      });
    }
  }

  /**
   * 获取学习统计
   * @param {number} days - 统计天数
   * @returns {Object} 学习统计
   */
  getLearningStats(days = 7) {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - (days - 1) * 24 * 60 * 60 * 1000);
    
    const stats = {
      period: { startDate, endDate, days },
      totals: {
        questions: 0,
        correctAnswers: 0,
        sessions: 0,
        timeSpent: 0
      },
      averages: {
        questionsPerDay: 0,
        accuracy: 0,
        timePerDay: 0,
        timePerQuestion: 0
      },
      trends: {
        dailyQuestions: [],
        dailyAccuracy: [],
        dailyTime: []
      },
      streaks: {
        current: this.learningStreaks.get('current') || { count: 0 },
        max: this.learningStreaks.get('max') || { count: 0 }
      }
    };
    
    // 收集指定期间的数据
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateKey = this.getDateKey(date);
      const dailyRecord = this.dailyRecords.get(dateKey);
      
      if (dailyRecord) {
        stats.totals.questions += dailyRecord.totalQuestions;
        stats.totals.correctAnswers += dailyRecord.correctAnswers;
        stats.totals.sessions += dailyRecord.sessionsCount;
        stats.totals.timeSpent += dailyRecord.totalTime;
        
        stats.trends.dailyQuestions.push({
          date: dateKey,
          count: dailyRecord.totalQuestions
        });
        
        stats.trends.dailyAccuracy.push({
          date: dateKey,
          accuracy: dailyRecord.totalQuestions > 0 
            ? dailyRecord.correctAnswers / dailyRecord.totalQuestions 
            : 0
        });
        
        stats.trends.dailyTime.push({
          date: dateKey,
          time: dailyRecord.totalTime
        });
      } else {
        stats.trends.dailyQuestions.push({ date: dateKey, count: 0 });
        stats.trends.dailyAccuracy.push({ date: dateKey, accuracy: 0 });
        stats.trends.dailyTime.push({ date: dateKey, time: 0 });
      }
    }
    
    // 计算平均值
    stats.averages.questionsPerDay = stats.totals.questions / days;
    stats.averages.accuracy = stats.totals.questions > 0 
      ? stats.totals.correctAnswers / stats.totals.questions 
      : 0;
    stats.averages.timePerDay = stats.totals.timeSpent / days;
    stats.averages.timePerQuestion = stats.totals.questions > 0 
      ? stats.totals.timeSpent / stats.totals.questions 
      : 0;
    
    return stats;
  }

  /**
   * 生成会话总结
   * @returns {Object} 会话总结
   */
  generateSessionSummary() {
    if (!this.currentSession) return null;
    
    const session = this.currentSession;
    const duration = session.endTime - session.startTime;
    
    return {
      sessionId: session.id,
      activityType: session.activityType,
      duration,
      stats: { ...session.stats },
      performance: { ...session.performance },
      improvements: this.calculateImprovements(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 计算改进情况
   * @returns {Object} 改进分析
   */
  calculateImprovements() {
    // 与上次同类型会话比较
    const currentType = this.currentSession.activityType;
    const previousSessions = Array.from(this.sessionHistory.values())
      .filter(s => s.activityType === currentType && s.id !== this.currentSession.id)
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
    
    if (previousSessions.length === 0) {
      return { isFirstSession: true };
    }
    
    const lastSession = previousSessions[0];
    const current = this.currentSession.stats;
    const previous = lastSession.stats;
    
    return {
      accuracyChange: current.accuracy - previous.accuracy,
      speedChange: previous.averageResponseTime - current.averageResponseTime,
      streakImprovement: current.maxStreak - previous.maxStreak,
      volumeChange: current.totalQuestions - previous.totalQuestions
    };
  }

  /**
   * 生成学习建议
   * @returns {string[]} 建议列表
   */
  generateRecommendations() {
    const stats = this.currentSession.stats;
    const performance = this.currentSession.performance;
    const recommendations = [];
    
    // 基于准确率的建议
    if (stats.accuracy < 0.6) {
      recommendations.push('建议多复习基础句子，巩固语法知识');
    } else if (stats.accuracy > 0.9) {
      recommendations.push('表现优秀！可以尝试更高难度的句子');
    }
    
    // 基于响应时间的建议
    if (stats.averageResponseTime > 8000) {
      recommendations.push('可以通过多练习来提高反应速度');
    }
    
    // 基于难度分布的建议
    const difficultyStats = performance.byDifficulty;
    if (difficultyStats.advanced.total > 0 && difficultyStats.advanced.correct / difficultyStats.advanced.total < 0.5) {
      recommendations.push('高级句子准确率较低，建议先巩固中级内容');
    }
    
    return recommendations;
  }

  /**
   * 工具方法
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getDateKey(date) {
    return date.toISOString().split('T')[0];
  }

  saveToStorage() {
    try {
      const data = {
        sessionHistory: Array.from(this.sessionHistory.entries()),
        dailyRecords: Array.from(this.dailyRecords.entries()),
        learningStreaks: Array.from(this.learningStreaks.entries())
      };
      localStorage.setItem('sentence-alchemist-learning-tracker', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save learning tracker data:', error);
    }
  }

  loadFromStorage() {
    try {
      const saved = localStorage.getItem('sentence-alchemist-learning-tracker');
      if (saved) {
        const data = JSON.parse(saved);
        this.sessionHistory = new Map(data.sessionHistory || []);
        this.dailyRecords = new Map(data.dailyRecords || []);
        this.learningStreaks = new Map(data.learningStreaks || []);
      }
    } catch (error) {
      console.warn('Failed to load learning tracker data:', error);
    }
  }

  clear() {
    this.sessionHistory.clear();
    this.dailyRecords.clear();
    this.weeklyStats.clear();
    this.monthlyStats.clear();
    this.learningStreaks.clear();
    this.currentSession = null;
    localStorage.removeItem('sentence-alchemist-learning-tracker');
  }
}

// 导出单例实例
export const learningTracker = new LearningTracker();
