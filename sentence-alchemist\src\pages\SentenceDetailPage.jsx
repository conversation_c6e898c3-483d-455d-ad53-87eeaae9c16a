import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { ArrowLeft, Volume2, BookOpen, Tag, BarChart3, Repeat, HelpCircle } from 'lucide-react';
import { sampleSentences } from '../data/sampleSentences';

const PageContainer = styled.div`
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
`;

const BackButton = styled.button`
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  
  &:hover {
    background: #f7fafc;
  }
`;

const DetailCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const EnglishSentence = styled.h2`
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const ChineseSentence = styled.h3`
  font-size: 1.5rem;
  color: #4a5568;
  margin-bottom: 1.5rem;
`;

const PronunciationText = styled.div`
  font-family: monospace;
  color: #718096;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const ChinesePronunciationText = styled.div`
  font-family: 'Microsoft YaHei', sans-serif;
  color: #4a5568;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: #fef5e7;
  border-radius: 8px;
  border: 1px solid #f6e05e;
  font-size: 0.9rem;
  font-style: italic;
  text-align: center;
`;

const WordAnalysisContainer = styled.div`
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f0fff4;
  border-radius: 8px;
  border: 1px solid #9ae6b4;
`;

const WordAnalysisText = styled.div`
  line-height: 1.8;
  font-size: 1rem;
`;

const ClickableWord = styled.span`
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: #bee3f8;
    color: #2b6cb0;
  }

  &.selected {
    background: #4299e1;
    color: white;
  }
`;

const WordExplanation = styled.div`
  margin-top: 1rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  color: #4a5568;
`;

const SectionTitle = styled.h4`
  font-size: 1.25rem;
  color: #4a5568;
  margin: 1.5rem 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ExplanationText = styled.p`
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
`;

const TagComponent = styled.span`
  background: #ebf4ff;
  color: #4299e1;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const SceneTag = styled(TagComponent)`
  background: #faf5ff;
  color: #805ad5;
`;

const DifficultyTag = styled(TagComponent)`
  background: ${props => {
    switch (props.level) {
      case 'beginner': return '#e6fffa';
      case 'intermediate': return '#fffaf0';
      case 'advanced': return '#fff5f5';
      default: return '#e6fffa';
    }
  }};
  color: ${props => {
    switch (props.level) {
      case 'beginner': return '#38b2ac';
      case 'intermediate': return '#dd6b20';
      case 'advanced': return '#e53e3e';
      default: return '#38b2ac';
    }
  }};
`;

const SimilarSentencesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
`;

const SimilarSentenceCard = styled.div`
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #edf2f7;
    transform: translateY(-2px);
  }
`;

const SimilarEnglish = styled.div`
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const SimilarChinese = styled.div`
  color: #718096;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
`;

const StatCard = styled.div`
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #4a5568;
  margin-bottom: 0.25rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #718096;
`;

const PlayButton = styled.button`
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #3182ce;
  }
`;

const SentenceDetailPage = ({ sentenceId = 1, onBack }) => {
  const [sentence, setSentence] = useState(null);
  const [similarSentences, setSimilarSentences] = useState([]);
  const [selectedWord, setSelectedWord] = useState(null);

  // 简单的单词解释数据库
  const wordExplanations = {
    'where': { meaning: '哪里', type: '疑问副词', usage: '用于询问地点或位置' },
    'is': { meaning: '是', type: '系动词', usage: '表示存在或状态' },
    'the': { meaning: '这个/那个', type: '定冠词', usage: '特指某个事物' },
    'departure': { meaning: '出发，离开', type: '名词', usage: '指离开的行为或时间' },
    'gate': { meaning: '门，登机口', type: '名词', usage: '机场中登机的入口' },
    'can': { meaning: '能够，可以', type: '情态动词', usage: '表示能力或请求许可' },
    'i': { meaning: '我', type: '人称代词', usage: '第一人称单数主格' },
    'have': { meaning: '有，拥有', type: '动词', usage: '表示拥有或获得' },
    'a': { meaning: '一个', type: '不定冠词', usage: '表示单数的泛指' },
    'window': { meaning: '窗户', type: '名词', usage: '建筑物的开口，用于采光通风' },
    'seat': { meaning: '座位', type: '名词', usage: '供人坐的地方' },
    'please': { meaning: '请', type: '副词', usage: '用于礼貌地请求' },
    'how': { meaning: '如何，怎样', type: '疑问副词', usage: '询问方式或程度' },
    'much': { meaning: '多少', type: '限定词', usage: '询问数量或程度' },
    'does': { meaning: '做（助动词）', type: '助动词', usage: '用于构成疑问句和否定句' },
    'this': { meaning: '这个', type: '指示代词', usage: '指近处的事物' },
    'cost': { meaning: '花费，成本', type: '动词/名词', usage: '表示价格或代价' }
  };
  
  useEffect(() => {
    // 获取句子详情
    const selectedSentence = sampleSentences.find(s => s.id === sentenceId) || sampleSentences[0];
    setSentence(selectedSentence);
    
    // 获取相似句子（同场景或相同标签）
    const similar = sampleSentences
      .filter(s => s.id !== selectedSentence.id && 
                  (s.scene === selectedSentence.scene || 
                   s.tags.some(tag => selectedSentence.tags.includes(tag))))
      .slice(0, 3);
    setSimilarSentences(similar);
  }, [sentenceId]);
  
  if (!sentence) {
    return <PageContainer>加载中...</PageContainer>;
  }
  
  const playPronunciation = () => {
    // 这里应该实现真实的发音功能
    console.log('播放发音:', sentence.english);
    // 使用Web Speech API作为示例
    const utterance = new SpeechSynthesisUtterance(sentence.english);
    utterance.lang = 'en-US';
    window.speechSynthesis.speak(utterance);
  };
  
  const handleSimilarSentenceClick = (id) => {
    // 这里应该实现跳转到其他句子详情的功能
    console.log('查看相似句子:', id);
  };

  const handleWordClick = (word) => {
    const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
    if (wordExplanations[cleanWord]) {
      setSelectedWord({ word: cleanWord, ...wordExplanations[cleanWord] });
    } else {
      setSelectedWord({ word: cleanWord, meaning: '暂无解释', type: '未知', usage: '请查阅词典获取更多信息' });
    }
  };

  const renderClickableText = (text) => {
    const words = text.split(/(\s+)/);
    return words.map((word, index) => {
      if (word.trim()) {
        return (
          <ClickableWord
            key={index}
            onClick={() => handleWordClick(word)}
            className={selectedWord?.word === word.toLowerCase().replace(/[^\w]/g, '') ? 'selected' : ''}
          >
            {word}
          </ClickableWord>
        );
      }
      return word;
    });
  };
  
  return (
    <PageContainer>
      <BackButton onClick={onBack}>
        <ArrowLeft size={18} />
        返回
      </BackButton>
      
      <DetailCard>
        <EnglishSentence>{sentence.english}</EnglishSentence>
        <ChineseSentence>{sentence.chinese}</ChineseSentence>
        
        <PronunciationText>
          {sentence.pronunciation}
          <PlayButton onClick={playPronunciation}>
            <Volume2 size={18} />
          </PlayButton>
        </PronunciationText>

        {sentence.chinesePronunciation && (
          <ChinesePronunciationText>
            中文发音提示：{sentence.chinesePronunciation}
          </ChinesePronunciationText>
        )}
        
        <TagsContainer>
          <SceneTag>{sentence.scene}</SceneTag>
          <DifficultyTag level={sentence.difficulty}>{sentence.difficulty}</DifficultyTag>
          {sentence.tags.map((tag, index) => (
            <TagComponent key={index}>{tag}</TagComponent>
          ))}
        </TagsContainer>
        
        <SectionTitle>
          <HelpCircle size={18} />
          单词分析
        </SectionTitle>
        <WordAnalysisContainer>
          <WordAnalysisText>
            {renderClickableText(sentence.english)}
          </WordAnalysisText>
          {selectedWord && (
            <WordExplanation>
              <strong>{selectedWord.word}</strong> ({selectedWord.type})
              <br />
              <strong>含义：</strong>{selectedWord.meaning}
              <br />
              <strong>用法：</strong>{selectedWord.usage}
            </WordExplanation>
          )}
        </WordAnalysisContainer>

        <SectionTitle>
          <BookOpen size={18} />
          句子解释
        </SectionTitle>
        <ExplanationText>{sentence.explanation}</ExplanationText>
        
        <SectionTitle>
          <BarChart3 size={18} />
          学习统计
        </SectionTitle>
        <StatsContainer>
          <StatCard>
            <StatValue>{sentence.learningData.correctCount}</StatValue>
            <StatLabel>答对次数</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{sentence.learningData.totalAttempts}</StatValue>
            <StatLabel>总尝试次数</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>
              {sentence.learningData.totalAttempts > 0 
                ? Math.round((sentence.learningData.correctCount / sentence.learningData.totalAttempts) * 100) 
                : 0}%
            </StatValue>
            <StatLabel>正确率</StatLabel>
          </StatCard>
        </StatsContainer>
        
        <SectionTitle>
          <Repeat size={18} />
          相似句子
        </SectionTitle>
        <SimilarSentencesContainer>
          {similarSentences.map(similar => (
            <SimilarSentenceCard 
              key={similar.id}
              onClick={() => handleSimilarSentenceClick(similar.id)}
            >
              <SimilarEnglish>{similar.english}</SimilarEnglish>
              <SimilarChinese>{similar.chinese}</SimilarChinese>
            </SimilarSentenceCard>
          ))}
        </SimilarSentencesContainer>
      </DetailCard>
    </PageContainer>
  );
};

export default SentenceDetailPage;
