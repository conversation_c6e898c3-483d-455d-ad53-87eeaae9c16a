import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { ArrowLeft, Volume2, BookOpen, Tag, BarChart3, Repeat } from 'lucide-react';
import { sampleSentences } from '../data/sampleSentences';

const PageContainer = styled.div`
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
`;

const BackButton = styled.button`
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  
  &:hover {
    background: #f7fafc;
  }
`;

const DetailCard = styled.div`
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
`;

const EnglishSentence = styled.h2`
  font-size: 2rem;
  color: #2d3748;
  margin-bottom: 0.5rem;
`;

const ChineseSentence = styled.h3`
  font-size: 1.5rem;
  color: #4a5568;
  margin-bottom: 1.5rem;
`;

const PronunciationText = styled.div`
  font-family: monospace;
  color: #718096;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
`;

const SectionTitle = styled.h4`
  font-size: 1.25rem;
  color: #4a5568;
  margin: 1.5rem 0 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ExplanationText = styled.p`
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 1rem;
`;

const TagsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin: 1rem 0;
`;

const TagComponent = styled.span`
  background: #ebf4ff;
  color: #4299e1;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
`;

const SceneTag = styled(TagComponent)`
  background: #faf5ff;
  color: #805ad5;
`;

const DifficultyTag = styled(TagComponent)`
  background: ${props => {
    switch (props.level) {
      case 'beginner': return '#e6fffa';
      case 'intermediate': return '#fffaf0';
      case 'advanced': return '#fff5f5';
      default: return '#e6fffa';
    }
  }};
  color: ${props => {
    switch (props.level) {
      case 'beginner': return '#38b2ac';
      case 'intermediate': return '#dd6b20';
      case 'advanced': return '#e53e3e';
      default: return '#38b2ac';
    }
  }};
`;

const SimilarSentencesContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
`;

const SimilarSentenceCard = styled.div`
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #edf2f7;
    transform: translateY(-2px);
  }
`;

const SimilarEnglish = styled.div`
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
`;

const SimilarChinese = styled.div`
  color: #718096;
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
`;

const StatCard = styled.div`
  background: #f7fafc;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
`;

const StatValue = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #4a5568;
  margin-bottom: 0.25rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #718096;
`;

const PlayButton = styled.button`
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #3182ce;
  }
`;

const SentenceDetailPage = ({ sentenceId = 1, onBack }) => {
  const [sentence, setSentence] = useState(null);
  const [similarSentences, setSimilarSentences] = useState([]);
  
  useEffect(() => {
    // 获取句子详情
    const selectedSentence = sampleSentences.find(s => s.id === sentenceId) || sampleSentences[0];
    setSentence(selectedSentence);
    
    // 获取相似句子（同场景或相同标签）
    const similar = sampleSentences
      .filter(s => s.id !== selectedSentence.id && 
                  (s.scene === selectedSentence.scene || 
                   s.tags.some(tag => selectedSentence.tags.includes(tag))))
      .slice(0, 3);
    setSimilarSentences(similar);
  }, [sentenceId]);
  
  if (!sentence) {
    return <PageContainer>加载中...</PageContainer>;
  }
  
  const playPronunciation = () => {
    // 这里应该实现真实的发音功能
    console.log('播放发音:', sentence.english);
    // 使用Web Speech API作为示例
    const utterance = new SpeechSynthesisUtterance(sentence.english);
    utterance.lang = 'en-US';
    window.speechSynthesis.speak(utterance);
  };
  
  const handleSimilarSentenceClick = (id) => {
    // 这里应该实现跳转到其他句子详情的功能
    console.log('查看相似句子:', id);
  };
  
  return (
    <PageContainer>
      <BackButton onClick={onBack}>
        <ArrowLeft size={18} />
        返回
      </BackButton>
      
      <DetailCard>
        <EnglishSentence>{sentence.english}</EnglishSentence>
        <ChineseSentence>{sentence.chinese}</ChineseSentence>
        
        <PronunciationText>
          {sentence.pronunciation}
          <PlayButton onClick={playPronunciation}>
            <Volume2 size={18} />
          </PlayButton>
        </PronunciationText>
        
        <TagsContainer>
          <SceneTag>{sentence.scene}</SceneTag>
          <DifficultyTag level={sentence.difficulty}>{sentence.difficulty}</DifficultyTag>
          {sentence.tags.map((tag, index) => (
            <TagComponent key={index}>{tag}</TagComponent>
          ))}
        </TagsContainer>
        
        <SectionTitle>
          <BookOpen size={18} />
          句子解释
        </SectionTitle>
        <ExplanationText>{sentence.explanation}</ExplanationText>
        
        <SectionTitle>
          <BarChart3 size={18} />
          学习统计
        </SectionTitle>
        <StatsContainer>
          <StatCard>
            <StatValue>{sentence.learningData.correctCount}</StatValue>
            <StatLabel>答对次数</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>{sentence.learningData.totalAttempts}</StatValue>
            <StatLabel>总尝试次数</StatLabel>
          </StatCard>
          <StatCard>
            <StatValue>
              {sentence.learningData.totalAttempts > 0 
                ? Math.round((sentence.learningData.correctCount / sentence.learningData.totalAttempts) * 100) 
                : 0}%
            </StatValue>
            <StatLabel>正确率</StatLabel>
          </StatCard>
        </StatsContainer>
        
        <SectionTitle>
          <Repeat size={18} />
          相似句子
        </SectionTitle>
        <SimilarSentencesContainer>
          {similarSentences.map(similar => (
            <SimilarSentenceCard 
              key={similar.id}
              onClick={() => handleSimilarSentenceClick(similar.id)}
            >
              <SimilarEnglish>{similar.english}</SimilarEnglish>
              <SimilarChinese>{similar.chinese}</SimilarChinese>
            </SimilarSentenceCard>
          ))}
        </SimilarSentencesContainer>
      </DetailCard>
    </PageContainer>
  );
};

export default SentenceDetailPage;
