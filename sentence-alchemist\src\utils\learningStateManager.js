/**
 * 学习状态管理系统
 * 管理句子的学习状态、复习计划和掌握程度
 */

// 学习状态枚举
export const LEARNING_STATE = {
  NEW: 'new',                    // 新句子
  LEARNING: 'learning',          // 学习中
  REVIEWING: 'reviewing',        // 复习中
  MASTERED: 'mastered',         // 已掌握
  DIFFICULT: 'difficult',       // 困难句子
  FORGOTTEN: 'forgotten'        // 已遗忘
};

// 掌握程度等级
export const MASTERY_LEVEL = {
  UNKNOWN: 0,      // 未知
  POOR: 1,         // 差
  FAIR: 2,         // 一般
  GOOD: 3,         // 良好
  VERY_GOOD: 4,    // 很好
  EXCELLENT: 5     // 优秀
};

// 基础复习间隔（天数）- 基于SuperMemo 2算法优化
export const BASE_REVIEW_INTERVALS = {
  [MASTERY_LEVEL.UNKNOWN]: 0,
  [MASTERY_LEVEL.POOR]: 1,
  [MASTERY_LEVEL.FAIR]: 3,
  [MASTERY_LEVEL.GOOD]: 7,
  [MASTERY_LEVEL.VERY_GOOD]: 14,
  [MASTERY_LEVEL.EXCELLENT]: 30
};

// 遗忘曲线参数
export const FORGETTING_CURVE = {
  // 遗忘速度因子（基于艾宾浩斯遗忘曲线）
  DECAY_RATE: 0.5,
  // 记忆强度初始值
  INITIAL_STRENGTH: 1.0,
  // 最小记忆强度
  MIN_STRENGTH: 0.1,
  // 复习效果增强因子
  REVIEW_BOOST: 1.3
};

// 难度因子（影响间隔计算）
export const DIFFICULTY_FACTORS = {
  'beginner': 1.0,
  'intermediate': 1.2,
  'advanced': 1.5
};

/**
 * 高级间隔重复算法类
 * 基于SuperMemo 2算法和现代记忆研究
 */
export class AdvancedSpacedRepetition {
  constructor() {
    this.easinessFactor = 2.5; // 默认简易因子
    this.minEasiness = 1.3;    // 最小简易因子
    this.maxEasiness = 3.0;    // 最大简易因子
  }

  /**
   * 计算下次复习间隔
   * @param {Object} learningData - 学习数据
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   * @param {string} difficulty - 句子难度
   * @returns {number} 下次复习间隔（天数）
   */
  calculateNextInterval(learningData, isCorrect, responseTime = 0, difficulty = 'intermediate') {
    const currentInterval = learningData.currentInterval || 0;
    const repetitions = learningData.repetitions || 0;
    let easiness = learningData.easinessFactor || this.easinessFactor;

    // 根据答题质量调整简易因子
    const quality = this.calculateQuality(isCorrect, responseTime, difficulty);
    easiness = this.updateEasinessFactor(easiness, quality);

    let nextInterval;

    if (quality < 3) {
      // 答错或质量差，重新开始
      nextInterval = 1;
      learningData.repetitions = 0;
    } else {
      // 答对，计算下次间隔
      if (repetitions === 0) {
        nextInterval = 1;
      } else if (repetitions === 1) {
        nextInterval = 6;
      } else {
        nextInterval = Math.round(currentInterval * easiness);
      }
      learningData.repetitions = repetitions + 1;
    }

    // 应用难度因子
    const difficultyFactor = DIFFICULTY_FACTORS[difficulty] || 1.0;
    nextInterval = Math.round(nextInterval * difficultyFactor);

    // 更新学习数据
    learningData.currentInterval = nextInterval;
    learningData.easinessFactor = easiness;
    learningData.lastQuality = quality;

    return Math.max(1, nextInterval); // 最少1天
  }

  /**
   * 计算答题质量（0-5分）
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间（毫秒）
   * @param {string} difficulty - 句子难度
   * @returns {number} 质量分数（0-5）
   */
  calculateQuality(isCorrect, responseTime, difficulty) {
    if (!isCorrect) {
      return 0; // 答错直接0分
    }

    // 基础分数
    let quality = 4; // 答对基础分4分

    // 根据响应时间调整
    const expectedTime = this.getExpectedResponseTime(difficulty);
    const timeRatio = responseTime / expectedTime;

    if (timeRatio <= 0.5) {
      quality = 5; // 很快答对，5分
    } else if (timeRatio <= 1.0) {
      quality = 4; // 正常时间答对，4分
    } else if (timeRatio <= 2.0) {
      quality = 3; // 较慢答对，3分
    } else {
      quality = 2; // 很慢答对，2分
    }

    return quality;
  }

  /**
   * 更新简易因子
   * @param {number} currentEasiness - 当前简易因子
   * @param {number} quality - 答题质量（0-5）
   * @returns {number} 新的简易因子
   */
  updateEasinessFactor(currentEasiness, quality) {
    const newEasiness = currentEasiness + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02));
    return Math.max(this.minEasiness, Math.min(this.maxEasiness, newEasiness));
  }

  /**
   * 获取期望响应时间
   * @param {string} difficulty - 句子难度
   * @returns {number} 期望响应时间（毫秒）
   */
  getExpectedResponseTime(difficulty) {
    const timeMap = {
      'beginner': 4000,
      'intermediate': 6000,
      'advanced': 8000
    };
    return timeMap[difficulty] || 6000;
  }

  /**
   * 计算记忆强度
   * @param {Object} learningData - 学习数据
   * @returns {number} 记忆强度（0-1）
   */
  calculateMemoryStrength(learningData) {
    if (!learningData.lastReviewed) {
      return FORGETTING_CURVE.INITIAL_STRENGTH;
    }

    const daysSinceReview = (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24);
    const interval = learningData.currentInterval || 1;
    const easiness = learningData.easinessFactor || this.easinessFactor;

    // 基于遗忘曲线计算记忆强度
    const decayRate = FORGETTING_CURVE.DECAY_RATE / easiness;
    const strength = Math.exp(-decayRate * daysSinceReview / interval);

    return Math.max(FORGETTING_CURVE.MIN_STRENGTH, strength);
  }

  /**
   * 预测遗忘概率
   * @param {Object} learningData - 学习数据
   * @param {number} futureDays - 未来天数
   * @returns {number} 遗忘概率（0-1）
   */
  predictForgettingProbability(learningData, futureDays = 0) {
    const memoryStrength = this.calculateMemoryStrength(learningData);
    const totalDays = futureDays + ((new Date() - new Date(learningData.lastReviewed || new Date())) / (1000 * 60 * 60 * 24));
    const interval = learningData.currentInterval || 1;
    const easiness = learningData.easinessFactor || this.easinessFactor;

    const decayRate = FORGETTING_CURVE.DECAY_RATE / easiness;
    const futureStrength = memoryStrength * Math.exp(-decayRate * totalDays / interval);

    return 1 - futureStrength;
  }
}

/**
 * 学习状态管理器类
 */
export class LearningStateManager {
  constructor() {
    this.stateHistory = new Map(); // 存储每个句子的状态历史
    this.reviewQueue = new Set();  // 复习队列
    this.difficultyQueue = new Set(); // 困难句子队列
    this.spacedRepetition = new AdvancedSpacedRepetition(); // 高级间隔重复算法
  }

  /**
   * 获取句子的当前学习状态
   * @param {Object} sentence - 句子对象
   * @returns {string} 学习状态
   */
  getLearningState(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || learningData.totalAttempts === 0) {
      return LEARNING_STATE.NEW;
    }
    
    const accuracy = learningData.correctCount / learningData.totalAttempts;
    const masteryLevel = learningData.masteryLevel || 0;
    const totalAttempts = learningData.totalAttempts;
    
    // 判断是否需要复习
    if (this.needsReview(sentence)) {
      return LEARNING_STATE.REVIEWING;
    }
    
    // 判断是否已掌握
    if (masteryLevel >= MASTERY_LEVEL.VERY_GOOD && accuracy >= 0.8 && totalAttempts >= 3) {
      return LEARNING_STATE.MASTERED;
    }
    
    // 判断是否困难
    if (accuracy < 0.4 && totalAttempts >= 3) {
      return LEARNING_STATE.DIFFICULT;
    }
    
    // 判断是否遗忘
    if (this.isForgotten(sentence)) {
      return LEARNING_STATE.FORGOTTEN;
    }
    
    // 默认为学习中
    return LEARNING_STATE.LEARNING;
  }

  /**
   * 更新句子的学习状态
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  updateLearningState(sentence, isCorrect, responseTime = 0) {
    // 添加安全检查
    if (!sentence) {
      console.error('updateLearningState: sentence is null or undefined');
      return;
    }

    if (!sentence.learningData) {
      sentence.learningData = {
        correctCount: 0,
        totalAttempts: 0,
        lastReviewed: null,
        nextReview: null,
        masteryLevel: MASTERY_LEVEL.UNKNOWN,
        averageResponseTime: 0,
        recentAccuracy: []
      };
    }

    const learningData = sentence.learningData;

    // 确保recentAccuracy数组存在
    if (!learningData.recentAccuracy) {
      learningData.recentAccuracy = [];
    }

    // 更新基础统计
    learningData.totalAttempts++;
    if (isCorrect) {
      learningData.correctCount++;
    }

    // 更新最近准确率（保留最近10次记录）
    learningData.recentAccuracy.push(isCorrect ? 1 : 0);
    if (learningData.recentAccuracy.length > 10) {
      learningData.recentAccuracy.shift();
    }
    
    // 更新平均响应时间
    if (responseTime > 0) {
      const currentAvg = learningData.averageResponseTime || 0;
      const attempts = learningData.totalAttempts;
      learningData.averageResponseTime = (currentAvg * (attempts - 1) + responseTime) / attempts;
      learningData.lastResponseTime = responseTime; // 记录最后一次响应时间
    }
    
    // 更新掌握程度
    this.updateMasteryLevel(sentence, isCorrect, responseTime);
    
    // 更新复习时间
    this.updateReviewSchedule(sentence);
    
    // 更新队列状态
    this.updateQueues(sentence);
    
    // 记录状态历史
    this.recordStateHistory(sentence, isCorrect, responseTime);
  }

  /**
   * 更新掌握程度
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  updateMasteryLevel(sentence, isCorrect, responseTime) {
    const learningData = sentence.learningData;
    const currentLevel = learningData.masteryLevel || MASTERY_LEVEL.UNKNOWN;
    
    if (isCorrect) {
      // 答对了，可能提升掌握程度
      const recentAccuracy = this.getRecentAccuracy(sentence);
      
      if (recentAccuracy >= 0.9 && learningData.totalAttempts >= 3) {
        // 最近表现很好，提升掌握程度
        learningData.masteryLevel = Math.min(MASTERY_LEVEL.EXCELLENT, currentLevel + 1);
      } else if (recentAccuracy >= 0.7 && currentLevel < MASTERY_LEVEL.GOOD) {
        // 表现不错，适度提升
        learningData.masteryLevel = currentLevel + 1;
      }
      
      // 考虑响应时间因素
      if (responseTime > 0) {
        const expectedTime = this.getExpectedResponseTime(sentence);
        if (responseTime > expectedTime * 2) {
          // 响应时间过长，降低提升幅度
          learningData.masteryLevel = Math.max(currentLevel, learningData.masteryLevel - 1);
        }
      }
    } else {
      // 答错了，降低掌握程度
      const recentAccuracy = this.getRecentAccuracy(sentence);
      
      if (recentAccuracy < 0.3) {
        // 最近表现很差，大幅降低
        learningData.masteryLevel = Math.max(MASTERY_LEVEL.UNKNOWN, currentLevel - 2);
      } else if (recentAccuracy < 0.6) {
        // 表现一般，适度降低
        learningData.masteryLevel = Math.max(MASTERY_LEVEL.UNKNOWN, currentLevel - 1);
      }
    }
  }

  /**
   * 更新复习计划（使用高级间隔重复算法）
   * @param {Object} sentence - 句子对象
   */
  updateReviewSchedule(sentence) {
    const learningData = sentence.learningData;

    learningData.lastReviewed = new Date();

    // 使用高级间隔重复算法计算下次复习间隔
    const isCorrect = learningData.recentAccuracy && learningData.recentAccuracy.length > 0
      ? learningData.recentAccuracy[learningData.recentAccuracy.length - 1] === 1
      : false;

    const responseTime = learningData.lastResponseTime || 0;
    const difficulty = sentence.difficulty || 'intermediate';

    const intervalDays = this.spacedRepetition.calculateNextInterval(
      learningData,
      isCorrect,
      responseTime,
      difficulty
    );

    learningData.nextReview = new Date(Date.now() + intervalDays * 24 * 60 * 60 * 1000);
    learningData.memoryStrength = this.spacedRepetition.calculateMemoryStrength(learningData);
  }

  /**
   * 更新队列状态
   * @param {Object} sentence - 句子对象
   */
  updateQueues(sentence) {
    const state = this.getLearningState(sentence);
    
    // 更新复习队列
    if (state === LEARNING_STATE.REVIEWING || this.needsReview(sentence)) {
      this.reviewQueue.add(sentence.id);
    } else {
      this.reviewQueue.delete(sentence.id);
    }
    
    // 更新困难句子队列
    if (state === LEARNING_STATE.DIFFICULT) {
      this.difficultyQueue.add(sentence.id);
    } else {
      this.difficultyQueue.delete(sentence.id);
    }
  }

  /**
   * 检查句子是否需要复习
   * @param {Object} sentence - 句子对象
   * @returns {boolean} 是否需要复习
   */
  needsReview(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.nextReview) {
      return false;
    }
    
    return new Date(learningData.nextReview) <= new Date();
  }

  /**
   * 检查句子是否被遗忘
   * @param {Object} sentence - 句子对象
   * @returns {boolean} 是否被遗忘
   */
  isForgotten(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.lastReviewed) {
      return false;
    }
    
    const daysSinceLastReview = (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24);
    const masteryLevel = learningData.masteryLevel || MASTERY_LEVEL.UNKNOWN;
    
    // 根据掌握程度判断遗忘阈值
    const forgetThreshold = {
      [MASTERY_LEVEL.UNKNOWN]: 3,
      [MASTERY_LEVEL.POOR]: 7,
      [MASTERY_LEVEL.FAIR]: 14,
      [MASTERY_LEVEL.GOOD]: 30,
      [MASTERY_LEVEL.VERY_GOOD]: 60,
      [MASTERY_LEVEL.EXCELLENT]: 90
    };
    
    return daysSinceLastReview > (forgetThreshold[masteryLevel] || 7);
  }

  /**
   * 获取最近的准确率
   * @param {Object} sentence - 句子对象
   * @returns {number} 最近准确率
   */
  getRecentAccuracy(sentence) {
    const learningData = sentence.learningData;
    
    if (!learningData || !learningData.recentAccuracy || learningData.recentAccuracy.length === 0) {
      return 0;
    }
    
    const sum = learningData.recentAccuracy.reduce((a, b) => a + b, 0);
    return sum / learningData.recentAccuracy.length;
  }

  /**
   * 获取期望响应时间
   * @param {Object} sentence - 句子对象
   * @returns {number} 期望响应时间（毫秒）
   */
  getExpectedResponseTime(sentence) {
    const difficultyTime = {
      'beginner': 5000,
      'intermediate': 8000,
      'advanced': 12000
    };
    
    return difficultyTime[sentence.difficulty] || 8000;
  }

  /**
   * 记录状态历史
   * @param {Object} sentence - 句子对象
   * @param {boolean} isCorrect - 是否答对
   * @param {number} responseTime - 响应时间
   */
  recordStateHistory(sentence, isCorrect, responseTime) {
    if (!this.stateHistory.has(sentence.id)) {
      this.stateHistory.set(sentence.id, []);
    }
    
    const history = this.stateHistory.get(sentence.id);
    history.push({
      timestamp: new Date(),
      isCorrect,
      responseTime,
      state: this.getLearningState(sentence),
      masteryLevel: sentence.learningData?.masteryLevel || 0
    });
    
    // 保留最近50条记录
    if (history.length > 50) {
      history.shift();
    }
  }

  /**
   * 获取需要复习的句子
   * @param {Object[]} sentences - 句子数组
   * @returns {Object[]} 需要复习的句子
   */
  getReviewSentences(sentences) {
    return sentences.filter(sentence => this.needsReview(sentence));
  }

  /**
   * 获取困难句子
   * @param {Object[]} sentences - 句子数组
   * @returns {Object[]} 困难句子
   */
  getDifficultSentences(sentences) {
    return sentences.filter(sentence => 
      this.getLearningState(sentence) === LEARNING_STATE.DIFFICULT
    );
  }

  /**
   * 获取学习统计信息
   * @param {Object[]} sentences - 句子数组
   * @returns {Object} 统计信息
   */
  getLearningStats(sentences) {
    const stats = {
      total: sentences.length,
      byState: {},
      byMasteryLevel: {},
      needReview: 0,
      mastered: 0,
      averageMasteryLevel: 0
    };
    
    let totalMasteryLevel = 0;
    
    sentences.forEach(sentence => {
      const state = this.getLearningState(sentence);
      const masteryLevel = sentence.learningData?.masteryLevel || 0;
      
      stats.byState[state] = (stats.byState[state] || 0) + 1;
      stats.byMasteryLevel[masteryLevel] = (stats.byMasteryLevel[masteryLevel] || 0) + 1;
      
      if (this.needsReview(sentence)) {
        stats.needReview++;
      }
      
      if (state === LEARNING_STATE.MASTERED) {
        stats.mastered++;
      }
      
      totalMasteryLevel += masteryLevel;
    });
    
    stats.averageMasteryLevel = sentences.length > 0 ? totalMasteryLevel / sentences.length : 0;
    
    return stats;
  }

  /**
   * 获取优先复习的句子（基于遗忘概率排序）
   * @param {Object[]} sentences - 句子数组
   * @param {number} limit - 返回数量限制
   * @returns {Object[]} 按优先级排序的复习句子
   */
  getPriorityReviewSentences(sentences, limit = 10) {
    const reviewSentences = this.getReviewSentences(sentences);

    // 计算每个句子的复习优先级
    const prioritizedSentences = reviewSentences.map(sentence => {
      const learningData = sentence.learningData || {};
      const forgettingProb = this.spacedRepetition.predictForgettingProbability(learningData);
      const memoryStrength = this.spacedRepetition.calculateMemoryStrength(learningData);
      const daysSinceReview = learningData.lastReviewed
        ? (new Date() - new Date(learningData.lastReviewed)) / (1000 * 60 * 60 * 24)
        : 999;

      // 综合优先级分数（遗忘概率 + 时间因子 - 记忆强度）
      const priority = forgettingProb * 0.6 + Math.min(daysSinceReview / 30, 1) * 0.3 - memoryStrength * 0.1;

      return {
        ...sentence,
        reviewPriority: priority,
        forgettingProbability: forgettingProb,
        memoryStrength: memoryStrength,
        daysSinceReview: daysSinceReview
      };
    });

    // 按优先级排序并返回指定数量
    return prioritizedSentences
      .sort((a, b) => b.reviewPriority - a.reviewPriority)
      .slice(0, limit);
  }

  /**
   * 获取记忆强度分析
   * @param {Object[]} sentences - 句子数组
   * @returns {Object} 记忆强度分析结果
   */
  getMemoryStrengthAnalysis(sentences) {
    const analysis = {
      strong: [], // 记忆强度 > 0.8
      medium: [], // 记忆强度 0.4-0.8
      weak: [],   // 记忆强度 < 0.4
      averageStrength: 0,
      totalSentences: sentences.length
    };

    let totalStrength = 0;

    sentences.forEach(sentence => {
      const learningData = sentence.learningData || {};
      const strength = this.spacedRepetition.calculateMemoryStrength(learningData);

      totalStrength += strength;

      if (strength > 0.8) {
        analysis.strong.push({ ...sentence, memoryStrength: strength });
      } else if (strength > 0.4) {
        analysis.medium.push({ ...sentence, memoryStrength: strength });
      } else {
        analysis.weak.push({ ...sentence, memoryStrength: strength });
      }
    });

    analysis.averageStrength = sentences.length > 0 ? totalStrength / sentences.length : 0;

    return analysis;
  }

  /**
   * 预测未来复习负荷
   * @param {Object[]} sentences - 句子数组
   * @param {number} days - 预测天数
   * @returns {Object} 未来复习负荷预测
   */
  predictReviewLoad(sentences, days = 7) {
    const prediction = {};

    for (let i = 1; i <= days; i++) {
      const futureDate = new Date(Date.now() + i * 24 * 60 * 60 * 1000);
      const reviewCount = sentences.filter(sentence => {
        const learningData = sentence.learningData;
        if (!learningData || !learningData.nextReview) return false;

        const nextReview = new Date(learningData.nextReview);
        return nextReview.toDateString() === futureDate.toDateString();
      }).length;

      prediction[i] = reviewCount;
    }

    return prediction;
  }

  /**
   * 获取学习效率分析
   * @param {Object[]} sentences - 句子数组
   * @returns {Object} 学习效率分析
   */
  getLearningEfficiencyAnalysis(sentences) {
    const analysis = {
      totalAttempts: 0,
      totalCorrect: 0,
      averageAccuracy: 0,
      averageResponseTime: 0,
      improvementTrend: 0, // 正数表示进步，负数表示退步
      efficiencyScore: 0   // 综合效率分数
    };

    let totalResponseTime = 0;
    let validResponseTimes = 0;
    let recentAccuracies = [];

    sentences.forEach(sentence => {
      const learningData = sentence.learningData;
      if (!learningData) return;

      analysis.totalAttempts += learningData.totalAttempts || 0;
      analysis.totalCorrect += learningData.correctCount || 0;

      if (learningData.averageResponseTime > 0) {
        totalResponseTime += learningData.averageResponseTime;
        validResponseTimes++;
      }

      if (learningData.recentAccuracy && learningData.recentAccuracy.length > 0) {
        const recentAcc = learningData.recentAccuracy.reduce((a, b) => a + b, 0) / learningData.recentAccuracy.length;
        recentAccuracies.push(recentAcc);
      }
    });

    analysis.averageAccuracy = analysis.totalAttempts > 0 ? analysis.totalCorrect / analysis.totalAttempts : 0;
    analysis.averageResponseTime = validResponseTimes > 0 ? totalResponseTime / validResponseTimes : 0;

    // 计算改进趋势（比较最近的准确率）
    if (recentAccuracies.length > 1) {
      const firstHalf = recentAccuracies.slice(0, Math.floor(recentAccuracies.length / 2));
      const secondHalf = recentAccuracies.slice(Math.floor(recentAccuracies.length / 2));

      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

      analysis.improvementTrend = secondAvg - firstAvg;
    }

    // 计算综合效率分数（准确率 * 0.6 + 速度因子 * 0.3 + 改进趋势 * 0.1）
    const speedFactor = analysis.averageResponseTime > 0 ? Math.max(0, 1 - analysis.averageResponseTime / 10000) : 0;
    analysis.efficiencyScore = analysis.averageAccuracy * 0.6 + speedFactor * 0.3 + Math.max(0, analysis.improvementTrend) * 0.1;

    return analysis;
  }

  /**
   * 清除所有数据
   */
  clear() {
    this.stateHistory.clear();
    this.reviewQueue.clear();
    this.difficultyQueue.clear();
  }
}

// 导出单例实例
export const learningStateManager = new LearningStateManager();
