/**
 * 游戏流程控制系统
 * 整合所有功能，管理整个句子匹配游戏的流程
 */

import { sampleSentences, getSmartRandomSentence, generateOptions } from '../data/sampleSentences.js';
import { answerValidator } from './answerValidator.js';
import { learningStateManager, LEARNING_STATE } from './learningStateManager.js';
import { createClassifier, createRecommender } from './sentenceClassifier.js';
import { learningTracker, ACTIVITY_TYPE } from './learningTracker.js';

// 游戏状态枚举
export const GAME_STATE = {
  IDLE: 'idle',
  LOADING: 'loading',
  QUESTION: 'question',
  FEEDBACK: 'feedback',
  COMPLETED: 'completed',
  PAUSED: 'paused'
};

// 游戏模式枚举
export const GAME_MODE = {
  PRACTICE: 'practice',      // 练习模式
  REVIEW: 'review',         // 复习模式
  CHALLENGE: 'challenge',   // 挑战模式
  ADAPTIVE: 'adaptive'      // 自适应模式
};

/**
 * 游戏控制器类
 */
export class GameController {
  constructor() {
    this.state = GAME_STATE.IDLE;
    this.mode = GAME_MODE.PRACTICE;
    this.currentQuestion = null;
    this.currentOptions = null;
    this.correctIndex = -1;
    this.questionStartTime = null;
    this.sessionStats = this.initSessionStats();
    this.userProfile = this.loadUserProfile();
    this.classifier = createClassifier(sampleSentences);
    this.recommender = createRecommender(sampleSentences, this.userProfile);
    this.usedQuestionIds = new Set();
    
    // 游戏配置
    this.config = {
      maxQuestionsPerSession: 20,
      feedbackDisplayTime: 3000,
      enableSound: true,
      enableAnimation: true,
      adaptiveDifficulty: true
    };
  }

  /**
   * 初始化会话统计
   * @returns {Object} 会话统计对象
   */
  initSessionStats() {
    return {
      questionsAnswered: 0,
      correctAnswers: 0,
      totalScore: 0,
      totalTime: 0,
      startTime: new Date(),
      endTime: null,
      streak: 0,
      maxStreak: 0,
      byDifficulty: {
        beginner: { correct: 0, total: 0 },
        intermediate: { correct: 0, total: 0 },
        advanced: { correct: 0, total: 0 }
      },
      byScene: {}
    };
  }

  /**
   * 加载用户档案
   * @returns {Object} 用户档案
   */
  loadUserProfile() {
    // 从localStorage加载用户档案
    const saved = localStorage.getItem('sentence-alchemist-profile');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.warn('Failed to load user profile:', e);
      }
    }
    
    // 默认用户档案
    return {
      totalQuestionsAnswered: 0,
      totalCorrectAnswers: 0,
      averageAccuracy: 0,
      recentAccuracy: 0,
      preferredScenes: [],
      currentLevel: 'beginner',
      achievements: [],
      studyStreak: 0,
      lastStudyDate: null
    };
  }

  /**
   * 保存用户档案
   */
  saveUserProfile() {
    try {
      localStorage.setItem('sentence-alchemist-profile', JSON.stringify(this.userProfile));
    } catch (e) {
      console.warn('Failed to save user profile:', e);
    }
  }

  /**
   * 开始新游戏
   * @param {string} mode - 游戏模式
   * @param {Object} options - 游戏选项
   */
  startGame(mode = GAME_MODE.PRACTICE, options = {}) {
    this.mode = mode;
    this.state = GAME_STATE.LOADING;
    this.sessionStats = this.initSessionStats();
    this.usedQuestionIds.clear();

    // 应用配置选项
    Object.assign(this.config, options);

    // 启动学习追踪
    const activityType = this.getActivityType(mode);
    this.currentSessionId = learningTracker.startSession(activityType, {
      mode,
      maxQuestions: this.config.maxQuestionsPerSession,
      ...options
    });

    // 根据模式初始化
    this.initializeGameMode();

    // 加载第一个问题
    this.loadNextQuestion();
  }

  /**
   * 根据模式初始化游戏
   */
  initializeGameMode() {
    switch (this.mode) {
      case GAME_MODE.REVIEW:
        // 复习模式：使用复习批次或需要复习的句子
        if (this.config.reviewBatch && this.config.reviewBatch.length > 0) {
          this.availableQuestions = this.config.reviewBatch.map(item => item.sentence);
          this.reviewBatch = this.config.reviewBatch;
        } else {
          this.availableQuestions = learningStateManager.getReviewSentences(sampleSentences);
        }
        break;

      case GAME_MODE.CHALLENGE:
        // 挑战模式：随机选择，但偏向困难句子
        this.availableQuestions = sampleSentences.filter(s =>
          s.difficulty === 'intermediate' || s.difficulty === 'advanced'
        );
        break;

      case GAME_MODE.ADAPTIVE:
        // 自适应模式：根据用户水平动态调整
        this.config.adaptiveDifficulty = true;
        this.availableQuestions = sampleSentences;
        break;

      default:
        // 练习模式：所有句子都可用
        this.availableQuestions = sampleSentences;
    }
  }

  /**
   * 加载下一个问题
   */
  loadNextQuestion() {
    try {
      if (this.sessionStats.questionsAnswered >= this.config.maxQuestionsPerSession) {
        this.completeGame();
        return;
      }

      this.state = GAME_STATE.LOADING;

      // 选择下一个句子
      const nextSentence = this.selectNextSentence();

      if (!nextSentence) {
        console.warn('No more sentences available, completing game');
        this.completeGame();
        return;
      }

      // 设置当前问题
      this.currentQuestion = nextSentence;
      this.usedQuestionIds.add(nextSentence.id);

      // 清除之前的结果
      this.currentResult = null;

      // 生成选项
      const optionsData = generateOptions(nextSentence);
      if (!optionsData || !optionsData.options || optionsData.correctIndex < 0) {
        console.error('Failed to generate options for sentence:', nextSentence);
        // 尝试下一个句子
        this.usedQuestionIds.delete(nextSentence.id);
        this.loadNextQuestion();
        return;
      }

      this.currentOptions = optionsData.options;
      this.correctIndex = optionsData.correctIndex;

      // 记录开始时间
      this.questionStartTime = Date.now();

      // 更新状态
      this.state = GAME_STATE.QUESTION;

      console.log('Loaded question:', {
        id: nextSentence.id,
        english: nextSentence.english,
        optionsCount: this.currentOptions.length,
        correctIndex: this.correctIndex
      });
    } catch (error) {
      console.error('Error in loadNextQuestion:', error);
      this.completeGame();
    }
  }

  /**
   * 选择下一个句子
   * @returns {Object|null} 选择的句子
   */
  selectNextSentence() {
    try {
      const excludeIds = Array.from(this.usedQuestionIds);

      if (this.mode === GAME_MODE.REVIEW) {
        // 复习模式特殊处理：使用复习批次
        if (this.reviewBatch && this.reviewBatch.length > 0) {
          const currentIndex = this.sessionStats.questionsAnswered;
          if (currentIndex < this.reviewBatch.length) {
            this.currentReviewItem = this.reviewBatch[currentIndex];
            return this.reviewBatch[currentIndex].sentence;
          }
          return null;
        }

        // 传统复习模式：从需要复习的句子中选择
        const reviewSentences = this.availableQuestions.filter(s =>
          !excludeIds.includes(s.id) && learningStateManager.needsReview(s)
        );

        if (reviewSentences.length === 0) {
          return null;
        }

        return reviewSentences[Math.floor(Math.random() * reviewSentences.length)];
      }

      // 其他模式：使用智能推荐
      if (!this.recommender) {
        console.error('Recommender not initialized, falling back to random selection');
        // 回退到简单的随机选择
        const availableSentences = sampleSentences.filter(s => !excludeIds.includes(s.id));
        if (availableSentences.length === 0) {
          return null;
        }
        return availableSentences[Math.floor(Math.random() * availableSentences.length)];
      }

      return this.recommender.recommendNext({
        excludeIds,
        targetDifficulty: this.getTargetDifficulty(),
        preferredScene: this.getPreferredScene()
      });
    } catch (error) {
      console.error('Error in selectNextSentence:', error);
      // 回退到简单的随机选择
      const excludeIds = Array.from(this.usedQuestionIds);
      const availableSentences = sampleSentences.filter(s => !excludeIds.includes(s.id));
      if (availableSentences.length === 0) {
        return null;
      }
      return availableSentences[Math.floor(Math.random() * availableSentences.length)];
    }
  }

  /**
   * 获取目标难度
   * @returns {string} 目标难度
   */
  getTargetDifficulty() {
    if (!this.config.adaptiveDifficulty) {
      return null;
    }
    
    const accuracy = this.sessionStats.questionsAnswered > 0 
      ? this.sessionStats.correctAnswers / this.sessionStats.questionsAnswered 
      : this.userProfile.averageAccuracy;
    
    if (accuracy > 0.8) {
      return 'advanced';
    } else if (accuracy > 0.6) {
      return 'intermediate';
    } else {
      return 'beginner';
    }
  }

  /**
   * 获取偏好场景
   * @returns {string|null} 偏好场景
   */
  getPreferredScene() {
    if (this.userProfile.preferredScenes && this.userProfile.preferredScenes.length > 0) {
      return this.userProfile.preferredScenes[0];
    }
    return null;
  }

  /**
   * 提交答案
   * @param {number} answerIndex - 用户选择的答案索引
   */
  submitAnswer(answerIndex) {
    if (this.state !== GAME_STATE.QUESTION) {
      console.warn('submitAnswer called but game state is not QUESTION:', this.state);
      return;
    }

    // 添加安全检查
    if (!this.currentQuestion) {
      console.error('submitAnswer: currentQuestion is null');
      return;
    }

    if (!this.currentOptions || answerIndex < 0 || answerIndex >= this.currentOptions.length) {
      console.error('submitAnswer: invalid answerIndex or currentOptions', { answerIndex, currentOptions: this.currentOptions });
      return;
    }

    const responseTime = Date.now() - this.questionStartTime;

    try {
      // 验证答案
      const result = answerValidator.validateAnswer(
        this.currentQuestion,
        answerIndex,
        this.currentOptions,
        this.correctIndex,
        responseTime
      );

      // 记录学习活动
      const learningData = this.currentQuestion.learningData || {};
      learningTracker.recordActivity({
        sentenceId: this.currentQuestion.id,
        sentence: this.currentQuestion,
        selectedAnswer: answerIndex,
        correctAnswer: this.correctIndex,
        isCorrect: result.isCorrect,
        responseTime: responseTime,
        difficulty: this.currentQuestion.difficulty,
        scene: this.currentQuestion.scene,
        masteryLevelBefore: learningData.masteryLevel || 0,
        memoryStrengthBefore: learningData.memoryStrength || 0
      });

      // 更新学习状态
      learningStateManager.updateLearningState(
        this.currentQuestion,
        result.isCorrect,
        responseTime
      );

      // 更新会话统计
      this.updateSessionStats(result);

      // 更新用户档案
      this.updateUserProfile(result);

      // 设置反馈状态
      this.state = GAME_STATE.FEEDBACK;
      this.currentResult = result;

      // 自动进入下一题（延迟显示反馈）
      setTimeout(() => {
        if (this.state === GAME_STATE.FEEDBACK) {
          this.loadNextQuestion();
        }
      }, this.config.feedbackDisplayTime);
    } catch (error) {
      console.error('Error in submitAnswer:', error);
      // 如果出错，重新加载问题
      this.loadNextQuestion();
    }
  }

  /**
   * 更新会话统计
   * @param {Object} result - 答题结果
   */
  updateSessionStats(result) {
    const stats = this.sessionStats;
    
    stats.questionsAnswered++;
    stats.totalTime += result.responseTime;
    stats.totalScore += result.score;
    
    if (result.isCorrect) {
      stats.correctAnswers++;
      stats.streak++;
      stats.maxStreak = Math.max(stats.maxStreak, stats.streak);
    } else {
      stats.streak = 0;
    }
    
    // 按难度统计
    const difficulty = this.currentQuestion.difficulty;
    if (!stats.byDifficulty[difficulty]) {
      stats.byDifficulty[difficulty] = { correct: 0, total: 0 };
    }
    stats.byDifficulty[difficulty].total++;
    if (result.isCorrect) {
      stats.byDifficulty[difficulty].correct++;
    }
    
    // 按场景统计
    const scene = this.currentQuestion.scene;
    if (!stats.byScene[scene]) {
      stats.byScene[scene] = { correct: 0, total: 0 };
    }
    stats.byScene[scene].total++;
    if (result.isCorrect) {
      stats.byScene[scene].correct++;
    }
  }

  /**
   * 更新用户档案
   * @param {Object} result - 答题结果
   */
  updateUserProfile(result) {
    const profile = this.userProfile;
    
    profile.totalQuestionsAnswered++;
    if (result.isCorrect) {
      profile.totalCorrectAnswers++;
    }
    
    // 更新平均准确率
    profile.averageAccuracy = profile.totalCorrectAnswers / profile.totalQuestionsAnswered;
    
    // 更新最近准确率（最近20题）
    if (!profile.recentResults) {
      profile.recentResults = [];
    }
    profile.recentResults.push(result.isCorrect ? 1 : 0);
    if (profile.recentResults.length > 20) {
      profile.recentResults.shift();
    }
    profile.recentAccuracy = profile.recentResults.reduce((a, b) => a + b, 0) / profile.recentResults.length;
    
    // 更新学习日期
    const today = new Date().toDateString();
    if (profile.lastStudyDate !== today) {
      if (profile.lastStudyDate === new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()) {
        profile.studyStreak++;
      } else {
        profile.studyStreak = 1;
      }
      profile.lastStudyDate = today;
    }
    
    // 保存档案
    this.saveUserProfile();
  }

  /**
   * 完成游戏
   */
  completeGame() {
    this.state = GAME_STATE.COMPLETED;
    this.sessionStats.endTime = new Date();

    // 结束学习追踪会话
    if (this.currentSessionId) {
      this.sessionSummary = learningTracker.endSession();
    }

    // 计算最终统计
    const finalStats = this.calculateFinalStats();

    // 检查成就
    this.checkAchievements(finalStats);

    console.log('Game completed with stats:', finalStats);
    console.log('Session summary:', this.sessionSummary);

    return finalStats;
  }

  /**
   * 获取活动类型
   * @param {string} gameMode - 游戏模式
   * @returns {string} 活动类型
   */
  getActivityType(gameMode) {
    const modeToActivityMap = {
      [GAME_MODE.PRACTICE]: ACTIVITY_TYPE.PRACTICE,
      [GAME_MODE.REVIEW]: ACTIVITY_TYPE.REVIEW,
      [GAME_MODE.CHALLENGE]: ACTIVITY_TYPE.CHALLENGE,
      [GAME_MODE.ADAPTIVE]: ACTIVITY_TYPE.ADAPTIVE
    };

    return modeToActivityMap[gameMode] || ACTIVITY_TYPE.PRACTICE;
  }

  /**
   * 计算最终统计
   * @returns {Object} 最终统计
   */
  calculateFinalStats() {
    const stats = this.sessionStats;
    const accuracy = stats.questionsAnswered > 0 ? stats.correctAnswers / stats.questionsAnswered : 0;
    const averageScore = stats.questionsAnswered > 0 ? stats.totalScore / stats.questionsAnswered : 0;
    const averageTime = stats.questionsAnswered > 0 ? stats.totalTime / stats.questionsAnswered : 0;
    
    return {
      ...stats,
      accuracy,
      averageScore,
      averageTime,
      duration: stats.endTime - stats.startTime
    };
  }

  /**
   * 检查成就
   * @param {Object} finalStats - 最终统计
   */
  checkAchievements(finalStats) {
    const achievements = [];
    
    // 准确率成就
    if (finalStats.accuracy === 1.0 && finalStats.questionsAnswered >= 10) {
      achievements.push('perfect_session');
    }
    
    // 连击成就
    if (finalStats.maxStreak >= 10) {
      achievements.push('streak_master');
    }
    
    // 速度成就
    if (finalStats.averageTime < 3000 && finalStats.accuracy > 0.8) {
      achievements.push('speed_demon');
    }
    
    // 添加到用户档案
    achievements.forEach(achievement => {
      if (!this.userProfile.achievements.includes(achievement)) {
        this.userProfile.achievements.push(achievement);
      }
    });
    
    if (achievements.length > 0) {
      this.saveUserProfile();
    }
    
    return achievements;
  }

  /**
   * 暂停游戏
   */
  pauseGame() {
    if (this.state === GAME_STATE.QUESTION) {
      this.state = GAME_STATE.PAUSED;
    }
  }

  /**
   * 恢复游戏
   */
  resumeGame() {
    if (this.state === GAME_STATE.PAUSED) {
      this.state = GAME_STATE.QUESTION;
      this.questionStartTime = Date.now(); // 重置计时
    }
  }

  /**
   * 获取当前游戏状态
   * @returns {Object} 游戏状态信息
   */
  getGameState() {
    return {
      state: this.state,
      mode: this.mode,
      currentQuestion: this.currentQuestion,
      currentOptions: this.currentOptions,
      correctIndex: this.correctIndex,
      sessionStats: this.sessionStats,
      userProfile: this.userProfile,
      currentResult: this.currentResult
    };
  }

  /**
   * 重置游戏
   */
  resetGame() {
    this.state = GAME_STATE.IDLE;
    this.currentQuestion = null;
    this.currentOptions = null;
    this.correctIndex = -1;
    this.questionStartTime = null;
    this.currentResult = null;
    this.usedQuestionIds.clear();
  }
}

// 导出单例实例
export const gameController = new GameController();
